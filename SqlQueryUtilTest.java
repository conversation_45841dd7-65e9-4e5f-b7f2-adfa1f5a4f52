package com.sinitek.sirm.nocode.support.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.List;
import java.util.Map;

/**
 * SqlQueryUtil测试类
 * 验证PostgreSQL JSON操作符检测和直接连接执行功能
 */
public class SqlQueryUtilTest {

    @Mock
    private DataSource dataSource;
    
    @Mock
    private Connection connection;
    
    @Mock
    private PreparedStatement preparedStatement;
    
    @Mock
    private ResultSet resultSet;
    
    @Mock
    private ResultSetMetaData metaData;

    private SqlQueryUtil sqlQueryUtil;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        sqlQueryUtil = new SqlQueryUtil();
        // 通过反射设置dataSource字段
        try {
            java.lang.reflect.Field field = SqlQueryUtil.class.getDeclaredField("dataSource");
            field.setAccessible(true);
            field.set(sqlQueryUtil, dataSource);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testContainsPostgreSQLJsonOperators_WithArrowOperator() {
        String sql = "SELECT data->>'name' FROM users";
        
        // 通过反射调用私有方法
        boolean result = invokeContainsPostgreSQLJsonOperators(sql);
        
        assertTrue(result, "应该检测到PostgreSQL JSON操作符 ->>");
    }

    @Test
    void testContainsPostgreSQLJsonOperators_WithJsonbArrayElements() {
        String sql = "SELECT * FROM jsonb_array_elements('[1,2,3]')";
        
        boolean result = invokeContainsPostgreSQLJsonOperators(sql);
        
        assertTrue(result, "应该检测到PostgreSQL JSON函数 jsonb_array_elements");
    }

    @Test
    void testContainsPostgreSQLJsonOperators_WithLateralJoin() {
        String sql = "SELECT elem->>'ZDInput_jvy2' AS subject FROM table LEFT JOIN LATERAL jsonb_array_elements(data) AS elem ON true";
        
        boolean result = invokeContainsPostgreSQLJsonOperators(sql);
        
        assertTrue(result, "应该检测到PostgreSQL LATERAL JOIN和JSON操作符");
    }

    @Test
    void testContainsPostgreSQLJsonOperators_WithRegularSQL() {
        String sql = "SELECT id, name FROM users WHERE age > 18";
        
        boolean result = invokeContainsPostgreSQLJsonOperators(sql);
        
        assertFalse(result, "普通SQL不应该被检测为包含PostgreSQL JSON操作符");
    }

    @Test
    void testIsSqlSafe_WithSelectStatement() {
        String sql = "SELECT * FROM users";
        
        boolean result = sqlQueryUtil.isSqlSafe(sql);
        
        assertTrue(result, "SELECT语句应该被认为是安全的");
    }

    @Test
    void testIsSqlSafe_WithInsertStatement() {
        String sql = "INSERT INTO users (name) VALUES ('test')";
        
        boolean result = sqlQueryUtil.isSqlSafe(sql);
        
        assertFalse(result, "INSERT语句应该被认为是不安全的");
    }

    @Test
    void testIsSqlSafe_WithPostgreSQLJsonQuery() {
        String sql = "SELECT elem->>'ZDInput_jvy2' AS subject FROM table LEFT JOIN LATERAL jsonb_array_elements(data) AS elem ON true";
        
        boolean result = sqlQueryUtil.isSqlSafe(sql);
        
        assertTrue(result, "PostgreSQL JSON查询应该被认为是安全的");
    }

    /**
     * 通过反射调用私有方法containsPostgreSQLJsonOperators
     */
    private boolean invokeContainsPostgreSQLJsonOperators(String sql) {
        try {
            java.lang.reflect.Method method = SqlQueryUtil.class.getDeclaredMethod("containsPostgreSQLJsonOperators", String.class);
            method.setAccessible(true);
            return (Boolean) method.invoke(sqlQueryUtil, sql);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试复杂的PostgreSQL JSON查询
     */
    @Test
    void testComplexPostgreSQLJsonQuery() {
        String complexSql = """
            SELECT 
                elem->>'ZDInput_jvy2' AS subject,
                AVG((elem->'ZDNumber_wx80')::numeric) AS avg_score
            FROM 
                public.zd_page_form_data_64 
            LEFT JOIN LATERAL jsonb_array_elements(form_data->'model'->'ZDChildForm_zolh') AS elem ON true
            WHERE 
                elem->>'ZDInput_jvy2' IN ('语文', '数学')
            GROUP BY 
                subject
            """;

        // 测试SQL安全性检查
        boolean isSafe = sqlQueryUtil.isSqlSafe(complexSql);
        assertTrue(isSafe, "复杂的PostgreSQL JSON查询应该被认为是安全的");

        // 测试PostgreSQL操作符检测
        boolean containsJsonOps = invokeContainsPostgreSQLJsonOperators(complexSql);
        assertTrue(containsJsonOps, "应该检测到PostgreSQL JSON操作符");
    }

    /**
     * 测试SQL注入防护
     */
    @Test
    void testSqlInjectionProtection() {
        String maliciousSql = "SELECT * FROM users; DROP TABLE users; --";
        
        boolean result = sqlQueryUtil.isSqlSafe(maliciousSql);
        
        assertFalse(result, "包含多条语句的SQL应该被认为是不安全的");
    }

    /**
     * 测试危险关键字检测
     */
    @Test
    void testDangerousKeywordDetection() {
        String[] dangerousSqls = {
            "DELETE FROM users",
            "UPDATE users SET name = 'hacked'",
            "DROP TABLE users",
            "CREATE TABLE malicious (id int)",
            "ALTER TABLE users ADD COLUMN hacked varchar(255)"
        };

        for (String sql : dangerousSqls) {
            boolean result = sqlQueryUtil.isSqlSafe(sql);
            assertFalse(result, "危险SQL应该被检测: " + sql);
        }
    }
}
