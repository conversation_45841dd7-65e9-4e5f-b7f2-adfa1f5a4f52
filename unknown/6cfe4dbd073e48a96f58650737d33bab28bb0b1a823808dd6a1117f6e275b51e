package com.sinitek.sirm.nocode.page.support.validator;

import com.sinitek.sirm.nocode.common.support.validator.base.IValidator;
import com.sinitek.sirm.nocode.page.dto.ZdPageBaseSettingDTO;
import com.sinitek.sirm.nocode.page.enumerate.SubmitJumpTypeEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0624
 * @since 1.0.0-SNAPSHOT
 */
@Component
@RequiredArgsConstructor
public class ZdPageBaseSettingValidator implements IValidator<ZdPageBaseSettingDTO> {
    private final IZdPageService pageService;

    @Override
    public String valid(ZdPageBaseSettingDTO zdPageBaseSettingDTO) {
        SubmitJumpTypeEnum submitJumpType = zdPageBaseSettingDTO.getSubmitJumpType();
        if (submitJumpType == SubmitJumpTypeEnum.APP_PAGE) {
            String innerPageCode = zdPageBaseSettingDTO.getInnerPageCode();
            if (innerPageCode == null) {
                return "跳转类型为应用内页面时，应用内页面编码不能为空";
            } else {
                String appCode = pageService.getAppCodeByCode(Collections.singletonList(innerPageCode));
                if (StringUtils.isBlank(appCode)) {
                    return "无效的页面编码";
                }

                // 判断是不是同一应用下的页面
                String formCode = zdPageBaseSettingDTO.getFormCode();
                if (!Objects.equals(appCode, pageService.getAppCodeByCode(Collections.singletonList(formCode)))) {
                    return "跳转类型为应用内页面时，应用内页面编码和表单编码必须在同一个应用下";

                }
            }

        }
        return null;
    }
}
