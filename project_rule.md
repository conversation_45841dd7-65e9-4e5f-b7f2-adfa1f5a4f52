# Sinitek NoCode Backend 项目规范

## 1. 项目概述

**零代码平台后端** - 基于动态JSON数据存储的企业级零代码平台后端系统

- **技术栈**: Spring Boot 2.x + MyBatis Plus + PostgreSQL + LLM集成
- **核心原理**: 通过PostgreSQL的JSONB字段动态存储表单配置和数据，实现零代码应用构建
- **架构模式**: 多模块Maven项目，分层架构，支持微服务化部署

## 2. 模块架构

```
sinitek-nocode-backend/
├── sinitek-nocode-api/           # API接口层 (DTO、Service接口)
├── sinitek-nocode-dal/           # 数据访问层 (Entity、Mapper、数据库脚本)
├── sinitek-nocode-assembly/      # 应用启动模块 (配置、启动类)
├── sinitek-nocode-service-design/   # 设计时服务 (表单设计、页面配置)
├── sinitek-nocode-service-runtime/  # 运行时服务 (数据操作、权限控制)
├── sinitek-nocode-service-llm/      # LLM服务 (AI辅助功能)
├── sinitek-nocode-gateway/          # 网关模块
└── sinitek-nocode-sdk/              # SDK模块
```

## 3. 核心业务模块

### 3.1 App模块 - 应用管理
- **包路径**: `com.sinitek.sirm.nocode.app.*`
- **核心表**: `zd_app`, `zd_app_manager`, `zd_app_oa_integration`
- **主要功能**: 应用创建、权限管理、OA集成配置、应用设置

### 3.2 Page模块 - 页面管理
- **包路径**: `com.sinitek.sirm.nocode.page.*`
- **核心表**: `zd_page`, `zd_page_auth`, `zd_page_scene`, `zd_page_base_setting`
- **主要功能**: 页面配置、权限设置、场景配置、基础设置

### 3.3 Form模块 - 表单管理
- **包路径**: `com.sinitek.sirm.nocode.form.*`
- **核心表**: `zd_page_form`, `zd_page_form_config`, `zd_page_form_data_*`
- **主要功能**: 表单设计、数据管理、字段映射、显示配置

### 3.4 Common模块 - 公共组件
- **包路径**: `com.sinitek.sirm.nocode.common.*`
- **主要功能**: 通用DTO、工具类、树结构、验证器、序列化器

## 4. 数据库设计原理

### 4.1 核心设计思想
- **JSON存储**: 使用PostgreSQL的JSONB字段存储动态表单配置和数据
- **分表策略**: 表单数据使用`zd_page_form_data_*`分表存储，支持横向扩展
- **GIN索引**: 对JSON字段创建GIN索引，优化查询性能

### 4.2 关键表结构
```sql
-- 表单配置表 (JSON存储表单设计)
zd_page_form.page_data: jsonb  

-- 表单数据表 (分表设计)
zd_page_form_data_*.form_data: jsonb

-- 权限配置 (JSON存储权限规则)
zd_page_auth.field_auth: jsonb
```

### 4.3 命名规范
- **表名**: `zd_` 前缀 + 模块名 + 实体名
- **字段**: 小写下划线命名
- **索引**: `in_` 前缀 + 表名 + 字段名
- **应用编码**: `app_` 前缀

### 4.4 万能SQL查询工具
- **工具类**: `SqlQueryUtil` (位于 `sinitek-nocode-dal` 模块)
- **功能**: 安全的动态SQL查询工具，只支持SELECT查询操作
- **特性**: SQL注入防护、参数化查询、结果限制、多语句检测
- **使用场景**: 大模型生成图表依赖、动态报表查询、数据统计分析、复杂业务查询

## 5. 开发规范

### 5.1 代码结构规范
```java
// DTO继承关系
ZdIdDTO -> ZdBaseDTO -> 具体DTO
BaseAuditEntity -> 审计字段基类

// Service接口命名
IZd{Module}{Entity}Service

// 实现类命名  
Zd{Module}{Entity}ServiceImpl
```

### 5.2 注解使用规范
- **验证注解**: 使用`@Validated`和分组验证 (`ZdIdDTO.Save.class`)
- **权限注解**: 使用`@ZdAppRight`进行应用权限控制
- **API文档**: 使用`@Api`和`@ApiOperation`
- **事务管理**: Service层使用`@Transactional`

### 5.3 异常处理规范
- 统一使用`RequestResult<T>`封装返回结果
- 业务异常继承框架异常基类
- 国际化错误信息存放在`messages-nocode_zh_CN.properties`

## 6. 关键配置

### 6.1 数据库配置
```yaml
mybatis-plus:
  type-enums-package: com.sinitek.sirm.nocode.*.enumerate
  typeHandlersPackage: com.sinitek.data.mybatis.typehandlers
  global-config:
    db-config:
      id-type: ASSIGN_ID
      update-strategy: ignored
```

### 6.2 LLM配置
```yaml
llm:
  sinitek-chat:
    server: http://192.168.22.247
    api-key: app-xxxxx
```

## 7. 开发最佳实践

### 7.1 新增业务模块
1. 在`sinitek-nocode-api`创建DTO和Service接口
2. 在`sinitek-nocode-dal`创建Entity和Mapper
3. 在对应服务模块实现Service和Controller
4. 添加数据库迁移脚本到`db/postgresql/`

### 7.2 JSON字段操作
```java
// 使用MyBatis Plus的JSON处理
@TableField(typeHandler = JacksonTypeHandler.class)
private Map<String, Object> formData;

// PostgreSQL JSON查询
formData->'fieldName' = 'value'
formData @> '{"status": 1}'
```

### 7.3 权限控制
- 应用级权限: `@ZdAppRight`
- 页面级权限: 通过`IZdPageAuthService`
- 数据级权限: 在Service层实现数据范围过滤

### 7.4 分表处理
```java
// 实现ITableName接口
public class ZdPageFormDataDTO implements ITableName {
    @Override
    public String getTableName() {
        return "zd_page_form_data_" + getTableSuffix();
    }
}
```

### 7.5 万能SQL查询工具使用
```java
// 注入SqlQueryUtil
@Autowired
private SqlQueryUtil sqlQueryUtil;

// 基本查询
String sql = "SELECT * FROM zd_app WHERE status = 1";
List<Map<String, Object>> result = sqlQueryUtil.executeQuery(sql);

// 参数化查询
String sql = "SELECT * FROM zd_app WHERE status = #{params.status}";
Map<String, Object> params = new HashMap<>();
params.put("status", 1);
List<Map<String, Object>> result = sqlQueryUtil.executeQuery(sql, params);

// 计数查询
Long count = sqlQueryUtil.executeCountQuery("zd_app", "status = 1");

// SQL安全性检查
if (sqlQueryUtil.isSqlSafe(sql)) {
    // 执行查询...
}

// PostgreSQL JSON查询（自动检测并处理Druid兼容性）
String complexSql = """
    SELECT
        elem->>'ZDInput_jvy2' AS subject,
        AVG((elem->'ZDNumber_wx80')::numeric) AS avg_score
    FROM
        public.zd_page_form_data_64
    LEFT JOIN LATERAL jsonb_array_elements(form_data->'model'->'ZDChildForm_zolh') AS elem ON true
    WHERE
        elem->>'ZDInput_jvy2' IN ('语文', '数学')
    GROUP BY
        subject
    """;
List<Map<String, Object>> result = sqlQueryUtil.executeQuery(complexSql);
```

**重要说明**：
- SqlQueryUtil已解决Druid WallFilter与PostgreSQL JSON操作符的兼容性问题
- 自动检测PostgreSQL特有语法（`->`、`->>`、`jsonb_array_elements`、`LATERAL`等）
- 对复杂PostgreSQL查询使用直接连接方式绕过Druid限制
- 保持所有安全检查机制，只允许SELECT查询

## 8. 重要常量

```java
// 应用编码前缀
AppConstant.APPCODE_PREFIX = "app_"

// 表名占位符
ZdCommonConstant.TABLE_NAME = "${ew.tableName}"

// 应用Token
ZdCommonConstant.APPLICATION_TOKEN = "app_access_token"
```

## 9. AI功能集成

### 9.1 LLM服务
- **模块**: `sinitek-nocode-service-llm`
- **功能**: 智能表单生成、SQL生成、图表生成
- **接口**: `/frontend/api/nocode/llm/*`

### 9.2 图像识别
- 支持上传图片进行表单识别
- 集成视觉大模型进行布局分析

## 10. 部署配置

### 10.1 环境配置
- **开发环境**: `application.yml`
- **测试环境**: `bootstrap-test.yml`
- **生产环境**: 通过环境变量覆盖

### 10.2 Docker支持
- 各模块包含Dockerfile
- 支持容器化部署
- 网关模块独立部署

---

**注意**: 本文档为AI Assistant参考，开发时请遵循以上规范，确保代码质量和系统稳定性。 
