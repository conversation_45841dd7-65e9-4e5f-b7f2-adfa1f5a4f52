package com.sinitek.sirm.nocode.common.utils;

import com.sinitek.sirm.nocode.common.constant.CacheKeyConstant;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0422
 * @description 组织工具类
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class ZdOrgUtil {
    @Resource
    private IOrgService orgService;


    @Cacheable(value = CacheKeyConstant.ROLE_CACHE_KEY, key = "#roleId")
    public List<String> getOrgIdsByRoleId(String roleId) {
        if (StringUtils.isNotBlank(roleId) && !Objects.equals(roleId, "0")) {
            return orgService.findEmployeeInserviceByRoleId(roleId).stream().map(Employee::getOrigId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 从列表中提取组织ID并构建组织名称映射
     *
     * @param list 数据列表
     * @return 组织ID与名称的映射表
     */
    public Map<String, String> getOrgName(List<String> list) {
        return getOrgName(list, s -> s);
    }

    /**
     * 从列表中提取组织ID并构建组织名称映射
     *
     * @param <T>           列表元素类型
     * @param list          数据列表
     * @param orgIdSupplier 组织ID提取函数
     * @return 组织ID与名称的映射表
     */
    public <T> Map<String, String> getOrgName(List<T> list, Function<T, String> orgIdSupplier) {
        return setOrgName(list, orgIdSupplier, null);
    }


    /**
     * 从列表中提取组织ID并构建组织名称映射
     *
     * @param <T>           列表元素类型
     * @param list          数据列表
     * @param orgIdSupplier 组织ID提取函数
     * @param consumer      组织名称设置函数
     * @return 组织ID与名称的映射表
     */
    public <T> Map<String, String> setOrgName(List<T> list, Function<T, String> orgIdSupplier, BiConsumer<T, String> consumer) {
        Map<String, String> orgNameMapByOrgIdList = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) {
            return orgNameMapByOrgIdList;
        }
        Map<String, ArrayList<T>> map = new HashMap<>(8);
        list.forEach(a -> {
            String apply = orgIdSupplier.apply(a);
            if (StringUtils.isNotBlank(apply)) {
                map.computeIfAbsent(apply, k -> new ArrayList<>()).add(a);
            }
        });
        if (CollectionUtils.isEmpty(map)) {
            return orgNameMapByOrgIdList;
        }
        Set<String> set = map.keySet();
        Map<String, String> resultMap = orgService.getOrgNameMapByOrgIdList(new ArrayList<>(set));
        if (Objects.nonNull(consumer)) {
            map.forEach((k, v) -> {
                String orgName = resultMap.get(k);
                v.forEach(a -> consumer.accept(a, orgName));
            });
        }
        return resultMap;
    }
}
