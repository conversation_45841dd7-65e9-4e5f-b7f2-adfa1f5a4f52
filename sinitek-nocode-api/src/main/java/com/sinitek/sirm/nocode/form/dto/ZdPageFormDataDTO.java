package com.sinitek.sirm.nocode.form.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import com.sinitek.sirm.common.enumerate.ApproveStatusEnum;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.enumerate.StatusEnum;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.common.support.mybatis.ITableName;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 表单数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "表单数据DTO")

public class ZdPageFormDataDTO extends BaseAuditEntity implements FormCodeSupplier, ITableName {

    /**
     * 表的名称
     */
    @JsonIgnore
    @TableField(exist = false)
    private String tableName;

    @JsonIgnore
    @ApiModelProperty("报告日期")
    private LocalDate reportDate;
    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码", required = true, example = "page_ca54fa8cfaab4aa58362421e542142d2")
    @TableField(exist = false)
    private String formCode;


    /**
     * 流程code
     */
    @ApiModelProperty(value = "工作流code", example = "page_b5d5f70d1848481a8165d54b072cf25b")
    @TableField(exist = false)
    private String processcode;
    /**
     * 收集表参数
     */
    @ApiModelProperty(value = "来源于网址参数上的值:&" + FormConstant.REPORT_PARAM + "=abcdef,这个时候就需要把abcdef放到这个参数里面 ", example = "abcdef")
    @TableField(exist = false)
    private String reportParam;

    /**
     * 提交人名称
     */
    @ApiModelProperty(value = "提交人名称", example = "管理员")
    @TableField(exist = false)
    private String creator;


    /**
     * 数据
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    @ApiModelProperty("表单数据")
    private String formData;

    /**
     * 提交的表单字段
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    @ApiModelProperty("提交的表单字段")
    private String submitField;

    /**
     * 审批状态，应用于审批模型
     *
     * @see ApproveStatusEnum
     */
    @ApiModelProperty("审批状态，应用于审批模型")
    @ApiEnumProperty(description = "审批状态，应用于审批模型", viewEnum = false, enumClazz = ApproveStatusEnum.class)
    private Integer approveStatus;

    /**
     * 业务数据状态
     */
    @ApiEnumProperty(description = "业务数据状态")
    private StatusEnum status;
}
