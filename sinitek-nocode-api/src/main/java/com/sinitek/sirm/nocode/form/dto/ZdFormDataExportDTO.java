package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0409
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "表单数据导出参数")
public class ZdFormDataExportDTO extends ZdFormDataBatchModelDTO {

    @NotEmpty(message = "导出字段不能为空", groups = {ZdIdDTO.View.class})
    @ApiModelProperty(value = "导出字段", example = "[\"processInstanceTitle\",\"textField_m7jtr073\",\"rateField_m7jtr074\",\"dateField_m7jtr075\",\"originator\",\"originatorCorp\",\"createTime\",\"modifiedTime\",\"currentNodeName\",\"processInstanceStatus\",\"processApprovedResult\"]")
    private List<String> fieldFilterVal;
}
