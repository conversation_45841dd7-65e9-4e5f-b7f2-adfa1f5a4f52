package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0604
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class ZdPageFormFieldMappingQueryDTO {

    /**
     * 表单编码
     */
    @NotBlank(message = "表单编码不能为空")
    @Length(max = 50, message = "表单编码不能超过50个字符")
    @ApiModelProperty(value = "表单编码", example = "form_123456", required = true)
    private String formCode;

    /**
     * 自定义字段名称
     */
    @NotBlank(message = "自定义字段名称不能为空")
    @Length(max = 300, message = "自定义字段名称不能超过300个字符")
    @ApiModelProperty(value = "自定义字段名称", example = "用户姓名", required = true)
    private String name;
}
