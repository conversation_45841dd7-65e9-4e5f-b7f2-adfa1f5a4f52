package com.sinitek.sirm.nocode.page.service;

import com.sinitek.sirm.nocode.page.dto.ZdPageBaseSettingDTO;

/**
 * <AUTHOR>
 * @version 2025-03-12 13:19:31
 * @description 针对表【zd_page_base_setting(页面的基本设置表)】的数据库操作Service
 */
public interface IZdPageBaseSettingService {

    /**
     * 保存或者修改页面基本设置
     *
     * @param zdPageBaseSettingDTO 页面基本设置
     * @return 是否成功
     */
    boolean saveOrUpdate(ZdPageBaseSettingDTO zdPageBaseSettingDTO);

    /**
     * 通过页面编码获取页面基本设置
     *
     * @param pageCode 页面编码
     * @return 页面基本设置
     */
    ZdPageBaseSettingDTO getByPageCode(String pageCode);

}
