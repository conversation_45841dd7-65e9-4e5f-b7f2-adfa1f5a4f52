package com.sinitek.sirm.nocode.form.support.condition.operation.postgresql;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.support.condition.OperationInterface;
import com.sinitek.sirm.nocode.form.support.condition.base.BetweenObject;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */

public class BetweenOperation implements OperationInterface {
    @Override
    public void apply(QueryWrapper<?> wrapper, String fieldName, Object value) {
        BetweenObject betweenObject = OperationInterface.betweenObject(value);
        if (Objects.nonNull(betweenObject)) {
            String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_BETWEEN, fieldName);
            wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, JsonUtil.toJsonString(betweenObject));
        }
    }

    @Override
    public OperatorEnum type() {
        return OperatorEnum.BETWEEN;
    }
}
