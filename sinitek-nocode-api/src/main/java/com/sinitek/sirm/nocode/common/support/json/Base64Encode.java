package com.sinitek.sirm.nocode.common.support.json;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.sinitek.sirm.common.utils.Base64Utils;

import java.io.IOException;

/**
 * base64加密，用于序列化
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
public class Base64Encode  extends JsonSerializer<String> {
    @Override
    public void serialize(String s, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        gen.writeObject(Base64Utils.encode(s));
    }
}
