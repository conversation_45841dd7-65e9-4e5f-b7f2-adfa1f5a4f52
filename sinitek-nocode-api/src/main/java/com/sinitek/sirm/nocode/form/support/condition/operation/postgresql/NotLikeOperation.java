package com.sinitek.sirm.nocode.form.support.condition.operation.postgresql;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.support.condition.OperationInterface;

/**
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */

public class NotLikeOperation implements OperationInterface {
    @Override
    public void apply(QueryWrapper<?> wrapper, String fieldName, Object value) {
        String like = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_NOT_LIKE, fieldName, value);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, like);
    }

    @Override
    public OperatorEnum type() {
        return OperatorEnum.NOT_LIKE;
    }
}
