package com.sinitek.sirm.nocode.common.web;

import com.sinitek.sirm.nocode.common.support.thread.ContextThreadCleaner;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025.0507
 * @since 1.0.0-SNAPSHOT
 */

public class RequestContext {
    private static final ThreadLocal<RequestContext> REQUEST_CONTEXT = new ThreadLocal<>();
    private final Map<String, Object> customizeDataMap = new HashMap<>();

    /**
     * appCodeKey
     */
    private static final String APP_CODE = "_appCode";


    private RequestContext() {
    }

    public static void begin() {
        REQUEST_CONTEXT.set(new RequestContext());
    }


    public static void init(String appCode) {
        getCustomizeDataMap().put(APP_CODE, appCode);

    }

    public static void end() {
        REQUEST_CONTEXT.remove();
    }

    public static Map<String, Object> getCustomizeDataMap() {
        Map<String, Object> customizeDataMap = new HashMap<>();
        RequestContext rc = REQUEST_CONTEXT.get();
        if (rc != null) {
            return rc.customizeDataMap;
        }
        return customizeDataMap;
    }

    public static void remove(String... keys) {
        Map<String, Object> map = getCustomizeDataMap();
        for (String key : keys) {
            map.remove(key);
        }
    }

    public static void clear() {
        getCustomizeDataMap().clear();
    }

    public static String getAppCode() {
        return (String) getCustomizeDataMap().get(APP_CODE);
    }

    static {
        // 静态注入清除者
        ContextThreadCleaner.register(RequestContext::end);
    }
}
