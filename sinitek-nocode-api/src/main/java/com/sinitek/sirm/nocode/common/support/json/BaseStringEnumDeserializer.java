package com.sinitek.sirm.nocode.common.support.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0313
 * @since 1.0.0-SNAPSHOT
 */

public class BaseStringEnumDeserializer<T extends BaseStringEnum> extends JsonDeserializer<T> implements ContextualDeserializer {
    private Class<T> targetClass;

    @Override
    public T deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        if (jsonParser == null || StringUtils.isBlank(jsonParser.getText())) {
            return null;
        }
        String text = jsonParser.getText();
        T[] enumConstants = targetClass.getEnumConstants();
        if (Objects.nonNull(enumConstants)) {
            for (T t : enumConstants) {
                if (Objects.equals(t.getValue(), text)) {
                    return t;
                }
            }
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext deserializationContext, BeanProperty property) throws JsonMappingException {
        targetClass = (Class<T>) deserializationContext.getContextualType().getRawClass();
        return new BaseStringEnumDeserializer<>(targetClass);
    }

    public BaseStringEnumDeserializer(Class<T> targetClass) {
        this.targetClass = targetClass;
    }

    public BaseStringEnumDeserializer() {
    }
}
