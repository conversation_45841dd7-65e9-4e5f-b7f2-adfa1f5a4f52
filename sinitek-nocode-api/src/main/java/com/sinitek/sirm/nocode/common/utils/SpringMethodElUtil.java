package com.sinitek.sirm.nocode.common.utils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0415
 * @description method el util
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class SpringMethodElUtil {
    @Resource
    private BeanFactory beanFactory;

    private final ParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    private final SpelExpressionParser parser = new SpelExpressionParser();


    /**
     * 判断是否是Spring EL表达式
     *
     * @param expression 表达式
     * @return 是否
     */
    public boolean isSpringEl(String expression) {
        return expression.contains("#");
    }


    /**
     * 获取Spring EL表达式的值
     *
     * @param spElString 表达式
     * @param joinPoint  连接点
     * @param valueType  返回值类型
     * @param <T>        值的泛型
     * @return 值
     */
    public <T> T value(String spElString, ProceedingJoinPoint joinPoint, Class<T> valueType) {
        MethodBasedEvaluationContext methodBasedEvaluationContext = new MethodBasedEvaluationContext(joinPoint.getTarget(), ((MethodSignature) joinPoint.getSignature()).getMethod(), joinPoint.getArgs(), nameDiscoverer);
        if (Objects.nonNull(beanFactory)) {
            methodBasedEvaluationContext.setBeanResolver(new BeanFactoryResolver(beanFactory));
        }
        Expression expression = parser.parseExpression(spElString);
        return expression.getValue(methodBasedEvaluationContext, valueType);
    }

    /**
     * 获取Spring EL表达式的值
     *
     * @param spElString 表达式
     * @param joinPoint  连接点
     * @return 值
     */
    public String value(String spElString, ProceedingJoinPoint joinPoint) {
        return value(spElString, joinPoint, String.class);

    }

}
