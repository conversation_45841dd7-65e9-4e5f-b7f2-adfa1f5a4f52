package com.sinitek.sirm.nocode.app.service;

import com.sinitek.sirm.nocode.app.dto.ZdAppManagerDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppManagerSaveDTO;

/**
 * <AUTHOR>
 * @version 2025-03-12 09:07:47
 * @description 针对表【zd_app_manager(应用管理员)】的数据库操作Service
 */
public interface IZdAppManagerService {
    /**
     * @param appCode 应用编码
     * @param orgId   组织id
     * @return 是否有权限
     */
    boolean hasAuth(String appCode, String orgId);

    boolean hasAuth(Long appId, String orgId);

    /**
     * 保存应用管理员
     *
     * @param zdAppManagerDTO 参数
     * @return 是否保存成功
     */
    boolean save(ZdAppManagerDTO zdAppManagerDTO);

    /**
     * 保存应用管理员
     *
     * @param zdAppManagerSaveDTO 参数
     * @return 是否保存成功
     */
    boolean save(ZdAppManagerSaveDTO zdAppManagerSaveDTO);


}
