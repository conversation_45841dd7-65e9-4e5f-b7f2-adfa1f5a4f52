package com.sinitek.sirm.nocode.common.support.thread;

import com.sinitek.sirm.common.spring.SpringFactory;

import java.util.HashSet;
import java.util.Set;

/**
 * 线程变量清除者
 *
 * <AUTHOR>
 * @version 2025.0506
 * @since 1.0.0-SNAPSHOT
 */
public class ContextThreadCleaner {
    private static boolean init = false;
    private static final Set<Cleaner> CLEANER_LIST = new HashSet<>();

    /**
     * 接口
     */
    public interface Cleaner {
        /**
         * 清理线程变量
         */
        void cleanAll();
    }

    /**
     * 清除所有的线程变量
     */
    public static void cleanAll() {
        if (!init) {
            CLEANER_LIST.addAll(SpringFactory.getBeans(Cleaner.class));
            init = true;
        }
        CLEANER_LIST.forEach(Cleaner::cleanAll);
    }

    /**
     * 注册清除者
     *
     * @param cleaner 清除者
     */
    public static void register(Cleaner cleaner) {
        CLEANER_LIST.add(cleaner);
    }
}
