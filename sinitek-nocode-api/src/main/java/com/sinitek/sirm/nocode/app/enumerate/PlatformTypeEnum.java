package com.sinitek.sirm.nocode.app.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0401
 * @description 平台类型枚举
 * @since 1.0.0-SNAPSHOT
 */
@Getter
public enum PlatformTypeEnum implements BaseIntegerEnum {
    WE_COM(0, "企微"),
    DING(1, "钉钉");
    @JsonValue
    @ApiModelProperty("值")
    private final Integer value;
    @ApiModelProperty("中文名称")
    private final String label;

    PlatformTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static PlatformTypeEnum fromValue(Integer value) {
        return Arrays.stream(PlatformTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }

}
