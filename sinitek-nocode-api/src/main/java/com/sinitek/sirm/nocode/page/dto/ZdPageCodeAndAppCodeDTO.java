package com.sinitek.sirm.nocode.page.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0617
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "应用编码和表单编码DTO")
@Data
public class ZdPageCodeAndAppCodeDTO {

    @ApiModelProperty(value = "表单编码", example = "page_ca54fa8cfaab4aa58362421e542142d2")
    private String formCode;

    @ApiModelProperty(value = "应用编码", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
    private String appCode;
}
