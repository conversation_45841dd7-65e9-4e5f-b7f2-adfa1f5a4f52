package com.sinitek.sirm.nocode.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sinitek.sirm.framework.frontend.support.OrderNameDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0519
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "排序对象")
public class OrderItemDTO {
    @NotBlank(message = "字段名不能为空")
    @ApiModelProperty(value = "排序属性名称", required = true)
    @JsonDeserialize(
            using = OrderNameDeserializer.class
    )
    private String orderName;

    @NotBlank(message = "排序类型不能为空")
    @ApiModelProperty(value = "排序类型，asc-升序，desc-降序", required = true)
    private String orderType;
}
