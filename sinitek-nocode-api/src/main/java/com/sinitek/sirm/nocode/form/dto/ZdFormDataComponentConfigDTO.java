package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0313
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单数据显示字段个性化配置DTO")
@Data
public class ZdFormDataComponentConfigDTO {
    @ApiModelProperty("唯一标志，数据库字段")
    private String key;
    @ApiModelProperty("列展示顺序")
    private Integer showOrder;
    @ApiModelProperty("列是否显示")
    private Boolean showTerm;
    @ApiModelProperty("是否是固定列")
    private Boolean fixed;
    @ApiModelProperty("子级")
    private List<ZdFormDataComponentConfigDTO> children;

}
