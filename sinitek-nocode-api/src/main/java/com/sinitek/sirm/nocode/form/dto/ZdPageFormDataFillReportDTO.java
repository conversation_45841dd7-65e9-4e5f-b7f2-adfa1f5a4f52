package com.sinitek.sirm.nocode.form.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单填写后的报告")
@Data
public class ZdPageFormDataFillReportDTO {
    @ApiModelProperty(value = "总填报人数", example = "100")
    private Integer total;
    @ApiModelProperty(value = "收集表,跳转到管理页时，有用到", example = "2025-05-26")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate reportDate;
    @ApiModelProperty(value = "表单填写人员")
    private List<ZdPageFormDataFillerDTO> filledList;
    @ApiModelProperty(value = "表单未填写人员")
    private List<ZdPageFormDataFillerDTO> unFilledList;
}
