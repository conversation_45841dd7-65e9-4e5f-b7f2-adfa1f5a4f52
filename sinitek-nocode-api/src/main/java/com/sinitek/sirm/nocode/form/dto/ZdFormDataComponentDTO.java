package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.common.dto.OptionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0313
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "表单数据组件显示DTO")
@Data
public class ZdFormDataComponentDTO  extends ZdFormDataComponentConfigDTO{
    @ApiModelProperty("值的类型，例如：STRING,DOUBLE")
    private String valueType;
    @ApiModelProperty("是否支持排序")
    private Boolean supportSort;

    @ApiModelProperty("是否支持搜索")
    private Boolean supportSearch;

    @ApiModelProperty("可以选择的操作符")
    private List<OptionDTO<String>> operatorList;


}
