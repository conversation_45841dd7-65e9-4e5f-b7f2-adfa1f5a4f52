package com.sinitek.sirm.nocode.llm.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.dto.ZdKeyAndNameDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * AI模型配置
 *
 * <AUTHOR>
 * @version 2025.0627
 * @since 1.0.0-SNAPSHOT
 */
@Component
@ConfigurationProperties(
        prefix = "nocode.ai"
)
@Data
@ApiModel(description = "AI模型配置")
public class AiModelConfig {
    @ApiModelProperty("ai模型列表")
    private List<Model> models;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel(description = "AI配置")
    public static class Model extends ZdKeyAndNameDTO {
        @JsonIgnore
        @ApiModelProperty("请求地址")
        private String server;
        @ApiModelProperty("模型类型列表")
        private List<ModelType> modelTypes;

    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel(description = "ai模型类型")
    public static class ModelType extends ZdKeyAndNameDTO {
        @ApiModelProperty("ai应用列表")
        private List<ModelApp> modelApps;
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel(description = "ai应用")
    public static class ModelApp extends ZdKeyAndNameDTO {
        @JsonIgnore
        @ApiModelProperty("应用的token")
        private String apiKey;
    }

    /**
     * 根据appKey查找模型应用
     *
     * @param appKey 应用key
     * @return 模型应用
     */
    public ModelAppAll findByAppKey(String appKey) {
        if (appKey == null || CollectionUtils.isEmpty(models)) {
            return null;
        }
        for (Model model : models) {
            List<ModelType> modelTypes = model.getModelTypes();
            if (CollectionUtils.isEmpty(modelTypes)) {
                continue;
            }
            for (ModelType modelType : modelTypes) {
                List<ModelApp> modelApps = modelType.getModelApps();
                if (CollectionUtils.isEmpty(modelApps)) {
                    continue;
                }
                for (ModelApp modelApp : modelApps) {
                    if (appKey.equals(modelApp.getKey())) {
                        return new ModelAppAll(model, modelType, modelApp);
                    }
                }
            }
        }
        return null;
    }


    @ApiModel(description = "模型应用参数")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ModelAppAll {
        private Model model;
        private ModelType modelType;
        private ModelApp modelApp;
    }
}
