package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0528
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "页面保存结果DTO")
public class ZdPageSaveResultDTO extends ZdPageGroupSaveDTO {
    /**
     * 页面编码
     */
    @ApiModelProperty(value = "页面编码", example = "page_23dcc69621b342f4b46a33473c25f6b9")
    private String code;
    /**
     * 页面主键
     */
    @ApiModelProperty(value = "页面主键", example = "1927538457450778626")
    private Long id;
    /**
     * 页面类型{@link PageTypeEnum}
     */
    @ApiEnumProperty(required = true, example = "0")
    private PageTypeEnum pageType;
}
