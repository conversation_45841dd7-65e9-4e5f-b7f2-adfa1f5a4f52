package com.sinitek.sirm.nocode.common.support.swagger;

import cn.hutool.core.net.NetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import springfox.documentation.swagger.web.ApiResourceController;
import springfox.documentation.swagger.web.SwaggerResource;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0311
 * @since 1.0.0-SNAPSHOT
 */
@Component
@Slf4j
public class SwaggerJsonUrl implements ApplicationListener<ApplicationStartedEvent> {
    @Value("${server.port:8080}")
    private String serverPort;
    @Value("${server.servlet.context-path:}")
    private String contextPath;
    @Autowired(required = false)
    private ApiResourceController apiResourceController;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        if (apiResourceController == null) {
            return;
        }
        ResponseEntity<List<SwaggerResource>> listResponseEntity = apiResourceController.swaggerResources();
        List<SwaggerResource> body = listResponseEntity.getBody();
        if (!CollectionUtils.isEmpty(body)) {
            String url = getUri();
            body.forEach(s -> {
                // 获取地址
                String path = s.getUrl();
                log.info(
                        "\n----------------------------------------------------------\n\t swaggerJson地址 {}\n----------------------------------------------------------",
                        url + path);
            });
        }
    }

    /**
     * 获取主机地址
     *
     * @return 主机地址
     */
    private String getUri() {
        String hostUrl = "http://" + NetUtil.getLocalhostStr() + ":" + serverPort;
        if (!StringUtils.isEmpty(contextPath)) {
            String prefix = "";
            if (!contextPath.startsWith("/")) {
                prefix = "/";
            }
            hostUrl += prefix + contextPath;
        }
        return hostUrl;
    }
}
