package com.sinitek.sirm.nocode.common.support.coverts;

import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.core.convert.converter.GenericConverter;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 2025.0428
 * @description 枚举转换器
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class BaseEnumConvert<T extends Serializable, C extends BaseEnum<T>, E extends Enum<E>> implements GenericConverter {


    @Override
    public Set<ConvertiblePair> getConvertibleTypes() {
        Set<ConvertiblePair> convertiblePairs = new HashSet<>();
        convertiblePairs.add(new ConvertiblePair(String.class, BaseEnum.class));
        convertiblePairs.add(new ConvertiblePair(Integer.class, BaseEnum.class));
        return convertiblePairs;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Object convert(Object source, TypeDescriptor sourceType, TypeDescriptor targetType) {
        if (Objects.isNull(source) || "".equals(source)) {
            return null;
        }
        Class<?> type = targetType.getType();
        if (type.isEnum()) {
            Object oldSource = source;
            if (BaseIntegerEnum.class.isAssignableFrom(type)) {
                source = NumberTool.safeToInteger(source, -1);
            }
            // 枚举的话
            C[] enumConstants = (C[]) type.getEnumConstants();
            for (C c : enumConstants) {
                if (Objects.equals(c.getValue(), source)) {
                    return c;
                }
            }
            Class<?> aClass = sourceType.getClass();
            if (Objects.equals(aClass, String.class)) {
                String name = (String) oldSource;
                for (C c : enumConstants) {
                    if (Objects.equals(name, ((E) c).name())) {
                        return c;
                    }
                }
            }
        }
        return null;
    }
}
