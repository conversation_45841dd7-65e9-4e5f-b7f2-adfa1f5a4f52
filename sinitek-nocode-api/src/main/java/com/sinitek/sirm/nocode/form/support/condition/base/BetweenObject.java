package com.sinitek.sirm.nocode.form.support.condition.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BetweenObject {
    private Object min;
    private Object max;

    public static BetweenObject of(Object min, Object max) {
        return new BetweenObject(min, max);
    }
}
