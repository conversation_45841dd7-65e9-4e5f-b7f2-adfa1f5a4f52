package com.sinitek.sirm.nocode.form.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0604
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "页面表单结构")
@Data
public class ZdFormPageDataDTO {
    @ApiModelProperty(value = "组件的唯一标识", example = "ZDInput_dpk8")
    private String ref;
    @ApiModelProperty(value = "组件类型", example = "ZDSelectMultiple")
    private String componentName;
    @ApiModelProperty(value = "组件的属性", example = "{\"label\": \"下拉框-多选\"}")
    private Props props;
    @ApiModelProperty(value = "子类组件")
    private List<ZdFormPageDataDTO> children;


    @Data
    public static class Props {
        @ApiModelProperty(value = "组件的名称", example = "名字")
        private String label;
        @ApiModelProperty(value = "选项")
        private List<Map<String, Object>> options;
    }

    /**
     * 遍历当前表单数据节点及其所有子节点
     *
     * @param consumer 遍历操作的消费者函数，接收两个参数：
     *                 第一个参数表示父节点（当前节点的直接上级），
     *                 第二个参数表示当前节点
     * @implNote 该方法首先处理当前节点（以null作为父节点），然后递归处理所有子节点
     */
    public void walk(BiConsumer<ZdFormPageDataDTO, ZdFormPageDataDTO> consumer) {
        consumer.accept(null, this);
        walkChildren(consumer);
    }

    /**
     * 遍历子元素并执行消费者操作。
     *
     * @param consumer BiConsumer，接受当前对象和子对象作为参数，执行操作
     */
    private void walkChildren(BiConsumer<ZdFormPageDataDTO, ZdFormPageDataDTO> consumer) {
        // 如果子元素集合不为空，则进行遍历处理
        if (CollectionUtil.isNotEmpty(children)) {
            // 对每个子元素执行消费者操作并递归处理其子元素
            children.forEach(child -> {
                consumer.accept(this, child);
                child.walkChildren(consumer);
            });
        }
    }


    /**
     * 查找所有的表单组件
     *
     * @return 表单组件数据
     */
    public List<ZdComponentDTO> findZdForm(Predicate<ZdFormPageDataDTO> test) {
        Predicate<ZdFormPageDataDTO> predicate = a -> Objects.equals(a.getComponentName(), PageDataComponentTypeEnum.ZD_FORM.getValue());
        List<ZdFormPageDataDTO> zdForm = findTargetChild(predicate);
        if (Objects.nonNull(test)) {
            zdForm = zdForm.stream().filter(test).collect(Collectors.toList());
        }
        return zdForm.stream().map(child -> {
            ZdComponentDTO pageDataDTO = new ZdComponentDTO();
            pageDataDTO.setRef(child.getRef());
            pageDataDTO.setComponentName(child.getComponentName());
            pageDataDTO.setLabel(child.getProps().getLabel());
            return pageDataDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 递归查找指定类型的子节点
     *
     * @param test 符合要求的测试
     * @return 子节点
     */
    private List<ZdFormPageDataDTO> findTargetChild(Predicate<ZdFormPageDataDTO> test) {
        List<ZdFormPageDataDTO> list = new ArrayList<>();
        if (test.test(this)) {
            if (Objects.isNull(children)) {
                return list;
            }
            return children;
        } else if (CollectionUtil.isNotEmpty(children)) {
            children.forEach(child -> list.addAll(child.findTargetChild(test)));
        }
        return list;
    }


    /**
     * 构建页面组件数据传输对象(ZdComponentDTO)
     *
     * @param parentComponentName 父级组件名称，用于判断组件层级关系
     * @return 组装后的组件数据传输对象，包含处理后的标签命名和属性映射
     */
    public ZdComponentDTO makeComponentDTO(String parentComponentName) {
        ZdComponentDTO pageDataDTO = new ZdComponentDTO();
        pageDataDTO.setRef(getRef());
        String label = getProps().getLabel();
        // 父级是子表单的话，那么加上前缀
        if (Objects.equals(parentComponentName, PageDataComponentTypeEnum.ZD_CHILD_FORM.getValue())) {
            label = PageDataComponentTypeEnum.ZD_CHILD_FORM.getLabel() + "-" + label;
        }
        pageDataDTO.setLabel(label);
        pageDataDTO.setComponentName(getComponentName());
        pageDataDTO.setOptions(getProps().getOptions());
        return pageDataDTO;
    }
}
