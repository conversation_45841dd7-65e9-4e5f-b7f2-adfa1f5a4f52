package com.sinitek.sirm.nocode.form.support.condition.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ValueObject {
    private Object values;

    public static ValueObject of(Object value) {
        return new ValueObject(value);
    }
}
