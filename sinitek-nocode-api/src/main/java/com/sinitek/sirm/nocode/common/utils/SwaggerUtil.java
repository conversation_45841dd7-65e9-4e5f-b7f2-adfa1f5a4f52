package com.sinitek.sirm.nocode.common.utils;

import cn.hutool.core.util.ReflectUtil;
import com.sinitek.sirm.nocode.common.dto.ModelPropertyDTO;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0428
 * @description
 * @since 1.0.0-SNAPSHOT
 */
public class SwaggerUtil {
    private SwaggerUtil() {
    }

    public static List<ModelPropertyDTO> modelProperty(Class<?> tClass) {
        List<ModelPropertyDTO> result = new ArrayList<>();
        ReflectUtil.getFields(tClass, field -> {
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null) {
                boolean hidden = annotation.hidden();
                if (!hidden) {
                    result.add(getModelPropertyDTO(annotation, field));
                    return true;
                }
            }
            return false;
        });
        return result;
    }

    private static ModelPropertyDTO getModelPropertyDTO(ApiModelProperty annotation, Field field) {
        String value = annotation.value();
        String example = annotation.example();
        boolean required = annotation.required();
        ModelPropertyDTO modelPropertyDTO = new ModelPropertyDTO();
        modelPropertyDTO.setName(field.getName());
        modelPropertyDTO.setExample(example);
        modelPropertyDTO.setRequired(required);
        modelPropertyDTO.setDescription(value);
        return modelPropertyDTO;
    }
}
