package com.sinitek.sirm.nocode.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0408
 * @description 当前登陆人DTO
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "当前登陆人DTO")
public class ZdCurrentOrgIdDTO {
    @JsonIgnore
    @ApiModelProperty(value = "当前登陆人orgId", required = true, example = "999999001")
    private String currentOrgId;
}
