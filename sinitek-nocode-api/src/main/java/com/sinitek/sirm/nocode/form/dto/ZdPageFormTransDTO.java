package com.sinitek.sirm.nocode.form.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sinitek.sirm.nocode.common.support.json.Base64Encode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */

@ApiModel("转换后的表单数据")
@Data
public class ZdPageFormTransDTO {
    @ApiModelProperty(value = "转换后的组件")
    private List<ZdComponentDTO> pageDataDTOList;
    @JsonSerialize(using = Base64Encode.class)
    @ApiModelProperty(value = "表单配置,base64格式")
    private String pageData;
}
