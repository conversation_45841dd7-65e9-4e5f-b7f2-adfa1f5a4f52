package com.sinitek.sirm.nocode.page.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import com.sinitek.sirm.nocode.common.dto.ZdOrgObjectDTO;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.page.enumerate.MemberTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 页面权限DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdPageAuthSaveDTO extends ZdIdDTO {

    /**
     * 页面编码
     */
    @JsonIgnore
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String pageCode;

    /**
     * 数据权限类型，0:提交，1:数据权限
     */
    @NotNull(message = "数据权限类型不能为空")
    @ApiModelProperty(value = "数据权限类型，0:提交，1:数据权限", example = "0")
    private PageAuthTypeEnum authType;

    /**
     * 权限排序
     */
    @ApiModelProperty(value = "权限排序", example = "0")
    private Integer sort;

    /**
     * 权限名称
     */
    @NotEmpty(message = "权限名称不能为空")
    @ApiModelProperty(value = "权限名称", example = "全部提交权限", required = true)
    private String name;

    /**
     * 权限描述
     */
    @ApiModelProperty(value = "权限描述", example = "这个是关于全部人员可以提交的权限描述")
    private String description;


    @NotNull(message = "成员类型不能为空")
    @ApiEnumProperty(example = "0")
    private MemberTypeEnum memberType;

    /**
     * 当成员类型选择为自定的时候，存储人员id,是个人员id数组
     */
    @ApiModelProperty(value = "当成员类型选择为自定的时候，存储人员id,是个人员id数组", example = "[]")
    private List<String> memberOrgIdList;

    @ApiModelProperty(value = "组织结构信息")
    private List<ZdOrgObjectDTO> memberList;

    /**
     * 当成员类型选择为自定的时候，存储人员id,是个人员id数组
     */
    @JsonIgnore
    @ApiModelProperty(value = "当成员类型选择为自定的时候，存储人员id,是个人员id数组", example = "\"[121212,11111]\"")
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String memberOrgIds;

    /**
     * 操作权限，多个用逗号隔开
     */
    @NotNull(message = "操作权限不能为空")
    @ApiModelProperty(value = "操作权限，多个用逗号隔开", example = "[\"0\",\"1\",\"2\"]", required = true)
    private List<String> operationAuthList;

    /**
     * 操作权限，多个用逗号隔开
     */
    @JsonIgnore
    @ApiModelProperty(value = "操作权限，多个用逗号隔开", example = "0,1,2", required = true)
    private String operationAuth;

    /**
     * 数据范围，多个用逗号隔开
     */
    @ApiModelProperty(value = "数据范围，多个用逗号隔开", example = "[\"0\",\"1\",\"2\"]")
    private List<String> dataScopeList;

    /**
     * 数据范围，多个用逗号隔开
     */
    @JsonIgnore
    @ApiModelProperty(value = "数据范围，多个用逗号隔开", example = "0,1,2")
    private String dataScope;


    /**
     * 当数据范围类型为自定义部门时，存储自定义部门数据
     */

    @ApiModelProperty(value = "当数据类型为自定义部门时，存储自定义部门数据")
    private List<String> departmentIdList;

    /**
     * 当数据范围类型为自定义部门时，存储自定义部门数据
     */
    @JsonIgnore
    @ApiModelProperty(value = "当数据类型为自定义部门时，存储自定义部门数据", example = "\"[121212,11111]\"")
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String departmentIds;

    /**
     * 字段权限，当为空时，为表单字段字段状态，有具体的权限控制的话，是个json数据
     */
    @ApiModelProperty(value = "字段权限，当为空时，为表单字段字段状态，有具体的权限控制的话，是个json字符数据", example = "\"{name:\"read\"}\"")
    private String fieldAuthData;

    @ApiModelProperty(value = "0:继承表单设计中组件的状态,1:自定义，详细查看枚举 FieldAuthFlagEnum,数值类型", example = "1", required = true, dataType = "Integer")

    private Integer fieldAuth;

    /**
     * 自定义数据权限范围
     */
    @ApiModelProperty(value = "自定义数据权限范围，是个数组字符串")
    private String customDataScope;

    @ApiModelProperty(value = "表单编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    public void setFormcode(String formcode) {
        this.pageCode = formcode;
    }

    @NotBlank(message = "页面编码不能为空")
    public String getFormcode() {
        return this.pageCode;
    }

}
