package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 表单字段映射表DTO
 *
 * <AUTHOR>
 * @version 2025.0604
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "表单配置表DTO")
public class ZdPageFormFieldMappingDTO extends ZdIdDTO implements FormCodeSupplier {

    /**
     * 表单编码
     */
    @NotBlank(message = "表单编码不能为空")
    @Length(max = 50, message = "表单编码不能超过50个字符")
    @ApiModelProperty(value = "表单编码", example = "form_123456", required = true)
    private String formCode;

    /**
     * 自定义字段名称
     */
    @NotBlank(message = "自定义字段名称不能为空")
    @Length(max = 300, message = "自定义字段名称不能超过300个字符")
    @ApiModelProperty(value = "自定义字段名称", example = "用户姓名", required = true)
    private String name;

    /**
     * 自定义字段的code
     */
    @Length(max = 100, message = "自定义字段的code不能超过100个字符")
    @ApiModelProperty(value = "自定义字段的code", example = "user_name")
    private String code;
}
