package com.sinitek.sirm.nocode.form.service;

import com.sinitek.sirm.nocode.form.dto.ZdFormDataComponentConfigDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormShowConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-18 13:52:06
 * @description 针对表【zd_page_form_show_config(表单数据显示设置)】的数据库操作Service
 */
public interface IZdPageFormShowConfigService {
    /**
     * 保存或者更新显示配置
     *
     * @param pageFormShowConfigDTO 对象
     */

    void saveOrUpdate(ZdPageFormShowConfigDTO pageFormShowConfigDTO);

    /**
     * 根据表单编码获取显示配置
     *
     * @param formCode 表单编码
     * @param orgId    人员orgId
     * @return 显示配置
     */
    ZdPageFormShowConfigDTO getByFormCode(String formCode, String orgId);

    List<ZdFormDataComponentConfigDTO> findShowConfigFormCode(String formCode, String orgId);

}
