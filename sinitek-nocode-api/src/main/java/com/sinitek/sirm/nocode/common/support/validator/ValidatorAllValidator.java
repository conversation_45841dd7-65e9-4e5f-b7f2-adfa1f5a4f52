package com.sinitek.sirm.nocode.common.support.validator;

import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.nocode.common.support.validator.base.IValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0511
 * @description
 * @since 1.0.0-SNAPSHOT
 */
public class ValidatorAllValidator implements ConstraintValidator<ValidatorAll, Object> {
    private ValidatorAll validatorAll;

    @Override
    public void initialize(ValidatorAll constraintAnnotation) {
        this.validatorAll = constraintAnnotation;
    }


    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public boolean isValid(Object t, ConstraintValidatorContext context) {
        IValidator validator = null;
        Class<? extends IValidator> value = validatorAll.value();
        boolean assignableFrom = value.isAssignableFrom(t.getClass());
        if (assignableFrom) {
            validator = (IValidator) t;
        }
        if (Objects.isNull(validator)) {
            validator = SpringFactory.getBean(value);
        }
        if (Objects.isNull(validator)) {
            validator = BeanUtils.instantiateClass(value);
        }
        String errorMessage = validator.valid(t);
        if (StringUtils.isNoneBlank(errorMessage)) {
            // 获取消息模板
            String defaultConstraintMessageTemplate = context.getDefaultConstraintMessageTemplate();
            if (StringUtils.isBlank(defaultConstraintMessageTemplate)) {
                // 假如没有配置模板，则直接返回错误信息
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(errorMessage)
                        .addConstraintViolation();
            }
            return false;
        }
        return true;
    }
}
