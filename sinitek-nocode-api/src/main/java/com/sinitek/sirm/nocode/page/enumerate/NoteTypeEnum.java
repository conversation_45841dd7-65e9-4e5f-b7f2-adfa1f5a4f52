package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.sinitek.sirm.common.message.template.enumerate.SendModeTypeEnum;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0519
 * @see SendModeTypeEnum
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "通知类型枚举")
@Getter
public enum NoteTypeEnum implements BaseIntegerEnum {
    SYS_REMINDER(0, "平台", SendModeTypeEnum.SENDMODE_SYSREMINDER),
    WX_WORK(1, "企微", SendModeTypeEnum.SENDMODE_WXWORK),
    EMAIL(2, "邮件", SendModeTypeEnum.SENDMODE_EMAIL),
    ;
    /**
     * 值
     */
    private final Integer value;
    /**
     * 名称
     */
    private final String label;
    private final SendModeTypeEnum sendModeTypeEnum;

    NoteTypeEnum(Integer value, String label, SendModeTypeEnum sendModeTypeEnum) {
        this.value = value;
        this.label = label;
        this.sendModeTypeEnum = sendModeTypeEnum;
    }

    @JsonCreator
    public static NoteTypeEnum fromValue(Integer value) {
        return Arrays.stream(NoteTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }

    public static List<NoteTypeEnum> fromValue(String value) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<>();
        }
        Map<Integer, NoteTypeEnum> map = BaseEnum.map(NoteTypeEnum.class);
        String[] split = value.split(",");
        return Arrays.stream(split).map(a -> map.get(NumberTool.safeToInteger(a, -1))).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取 发送方式枚举
     *
     * @param noteTypes 通知类型
     * @return 发送方式
     */
    public static List<SendModeTypeEnum> sendModeTypeEnumList(String noteTypes) {
        return fromValue(noteTypes).stream().map(a -> a.sendModeTypeEnum).collect(Collectors.toList());
    }

    /**
     * 获取 发送方式枚举
     *
     * @param noteTypes 通知类型
     * @return 发送方式
     */
    public static List<SendModeTypeEnum> sendModeTypeEnumList(List<Integer> noteTypes) {
        Map<Integer, NoteTypeEnum> map = BaseEnum.map(NoteTypeEnum.class);
        return noteTypes.stream().map(map::get).filter(Objects::nonNull).map(NoteTypeEnum::getSendModeTypeEnum).collect(Collectors.toList());
    }

}
