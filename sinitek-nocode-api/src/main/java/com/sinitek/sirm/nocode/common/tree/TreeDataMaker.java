package com.sinitek.sirm.nocode.common.tree;


import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.nocode.common.dto.ZdBaseNodeEntity;
import com.sinitek.sirm.nocode.common.dto.ZdTreeDTO;
import com.sinitek.sirm.nocode.common.tree.interfaces.IFindParent;
import com.sinitek.sirm.nocode.common.tree.interfaces.IFinderBaseParent;
import com.sinitek.sirm.nocode.common.utils.CopyUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * tree 计算类
 *
 * <AUTHOR>
 * @version 2025.0417
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class TreeDataMaker<T extends ZdBaseNodeEntity> {
    /**
     * 结果类型
     */
    private final Class<? extends ZdTreeDTO<T>> rClass;
    /**
     * 寻找父级Id的方法
     */
    private IFindParent<T> findParent;
    private IFinderBaseParent<T> finderBaseParent;

    public TreeDataMaker(Class<? extends ZdTreeDTO<T>> rClass) {
        this.rClass = rClass;
    }


    /**
     * 是否包含无父级的数据
     */
    private boolean containNoParent = false;
    /**
     * 获取 哪个等级的值，默认是首个
     */
    private Integer startLevel = 0;
    /**
     * 是从哪个结尾的
     */
    private Integer endLevel;
    /**
     * 最终返回的值
     */

    private List<?> returnResult;
    /**
     * 最新的父级id
     */
    private Long minParentId = Long.MAX_VALUE;
    /**
     * 设置最顶层的id
     */
    private Object baseId;

    /**
     * 计算结果集
     *
     * @param tList 数据
     * @param <R>   结果泛型
     * @return listR
     */
    @SuppressWarnings("unchecked")
    public <R extends ZdTreeDTO<T>> List<R> calculate(List<T> tList) {
        if (CollectionUtils.isEmpty(tList)) {
            return new ArrayList<>();
        }
        //开始计算
        Map<Object, List<R>> parentMap = new HashMap<>(16);
        Map<Object, R> valueMap = new HashMap<>(16);
        List<R> result = new ArrayList<>();
        for (T m : tList) {
            makeRvalue(m, parentMap, valueMap, result);
        }
        if (finderBaseParent == null) {
            if (baseId == null) {
                baseId = minParentId;
            }
            //最小的（最外层的）数据 既折叠后的数据
            result.addAll(parentMap.get(baseId));
        }
        parentMap.values().forEach(v -> v.forEach(r -> makeChildren(r, parentMap, valueMap, result)));
        makeLevelNumber(result, 0);
        return (List<R>) returnResult;
    }


    private <R extends ZdTreeDTO<T>> List<T> calculateFlat(List<T> list, Predicate<R> predicate) {
        if (Objects.isNull(list) || list.isEmpty()) {
            return Collections.emptyList();
        }
        List<R> calculate = calculate(list);
        return flat(calculate, predicate);
    }


    @SuppressWarnings("unchecked")
    private <R extends ZdTreeDTO<T>> List<T> flat(List<R> calculate, Predicate<R> predicate) {
        List<T> tList = new ArrayList<>();
        for (R r : calculate) {
            T treeOrgData = r.getTreeOrgData();
            boolean flag = true;
            if (Objects.nonNull(predicate)) {
                flag = predicate.test(r);
            }
            if (flag) {
                tList.add(treeOrgData);
                List<R> children = (List<R>) r.getChildren();
                if (CollectionUtils.isNotEmpty(children)) {
                    List<T> ts = flat(children, predicate);
                    treeOrgData.setChildren(ts);
                }
            }
        }
        return tList;
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    public static <T extends ZdBaseNodeEntity, R extends ZdTreeDTO<T>> List<T> tree(List<T> tList, Predicate<R> predicate) {
        return new TreeDataMaker(ZdTreeDTO.class).calculateFlat(tList, predicate);
    }

    public static <T extends ZdBaseNodeEntity> List<T> tree(List<T> tList) {
        return tree(tList, null);
    }

    /**
     * 处理单个数据
     *
     * @param m         单个数据
     * @param parentMap 父级map
     * @param valueMap  数据 map
     * @param result    结果
     * @param <R>       结果泛型
     */
    private <R extends ZdTreeDTO<T>> void makeRvalue(T m, Map<Object, List<R>> parentMap, Map<Object, R> valueMap, List<R> result) {
        Object value = m.getId();
        Object parentValue = getParentValue(m);
        if (baseId == null && finderBaseParent == null) {
            value = NumberTool.safeToLong(value, 0L);
            parentValue = NumberTool.safeToLong(parentValue, 0L);
            if ((Long) parentValue < minParentId) {
                minParentId = (Long) parentValue;
            }
        }
        List<R> oneParent = parentMap.computeIfAbsent(parentValue, k -> new ArrayList<>());
        R r = CopyUtil.copyProperties(m, rClass);
        r.setTreeOrgData(m);
        r.setId(value);
        String label = m.getName();

        r.setLabel(label);
        r.setParentId(parentValue);

        valueMap.put(value, r);
        // 父级的id不能为自己的id
        if (!value.equals(parentValue)) {
            oneParent.add(r);
            if (finderBaseParent != null && finderBaseParent.isBase(m)) {
                result.add(r);
            }
        }
    }


    /**
     * 获取父级
     *
     * @param t 数据
     * @return 父级id
     */
    private Object getParentValue(T t) {
        Object parentValue;
        if (findParent != null) {
            parentValue = findParent.find(t);
        } else {
            parentValue = t.getParentId();
        }
        return parentValue;
    }

    /**
     * 设置树状结构的等级
     *
     * @param result      结果
     * @param levelNumber 等级
     * @param <R>         泛型
     */
    private <R extends ZdTreeDTO<T>> void makeLevelNumber(List<R> result, Integer levelNumber) {
        if (CollectionUtils.isNotEmpty(result)) {
            result.forEach(r -> {
                r.setLevelNumber(levelNumber);
                // 下一个值
                Integer nextLevelNumber = levelNumber + 1;
                // 看下是否有结束的等级
                if (endLevel != null) {
                    // 假如有结束的等级需要处理
                    // 假如是0那么只留0，
                    // 假如是1 ，留 0,1
                    if (endLevel <= levelNumber) {
                        r.setChildren(null);
                    } else {
                        makeLevelNumber(r.getChildren(), nextLevelNumber);
                    }
                } else {
                    makeLevelNumber(r.getChildren(), nextLevelNumber);
                }
            });
            //获取某个等级的值
            if (levelNumber.equals(startLevel)) {
                returnResult = result;
            }
        }
    }

    /**
     * 设置 子集数据
     *
     * @param r         结果集
     * @param parentMap 父级数据
     * @param valueMap  数据映射
     * @param result    结果
     * @param <R>       结果的泛型
     */
    @SuppressWarnings("unchecked")
    private <R extends ZdTreeDTO<T>> void makeChildren(R r, Map<Object, List<R>> parentMap, Map<Object, R> valueMap, List<R> result) {
        Object id = r.getId();
        List<R> nodesArray = parentMap.get(id);
        if (CollectionUtils.isNotEmpty(nodesArray)) {
            r.setChildren((List<ZdTreeDTO<T>>) nodesArray);
            R valueR = valueMap.get(id);
            // 条件，当父级id对应的数据为空时，并且 id不等于顶级id
            if (containNoParent && valueR == null && !id.equals(baseId)) {
                // 假如是 游离数据，设置为 顶级数据
                result.addAll(nodesArray);
            }
        }
    }


}
