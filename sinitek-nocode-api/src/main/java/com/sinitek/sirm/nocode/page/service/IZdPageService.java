package com.sinitek.sirm.nocode.page.service;

import com.sinitek.sirm.common.dto.OptionDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageCodeAndAppCodeDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageCustomUrlDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSaveResultDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageUpdateNameDTO;
import com.sinitek.sirm.nocode.page.enumerate.CustomUrlTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025-03-12 10:00:09
 * @description 针对表【zd_page(页面表)】的数据库操作Service
 */
public interface IZdPageService {
    /**
     * 更新页面名称
     *
     * @param zdPageUpdateNameDTO 页面名称
     * @return 是否成功
     */
    boolean updateName(ZdPageUpdateNameDTO zdPageUpdateNameDTO);

    /**
     * 获取页面名称
     *
     * @param code code
     * @return 名称
     */
    String getNameByCode(String code);


    /**
     * 更新页面url
     *
     * @param code code
     * @return url
     */
    String getUrlByCode(String code);


    /**
     * 通过子自定义url 获取 信息
     *
     * @param url  自定义地址
     * @param type 类型
     * @return 信息
     */
    ZdPageCodeAndAppCodeDTO getByUrl(String url, CustomUrlTypeEnum type);


    /**
     * 保存页面
     *
     * @param pageDTO 页面参数
     * @return pageCode
     */
    ZdPageSaveResultDTO savePage(ZdPageDTO pageDTO);

    /**
     * 通过页面编码删除页面
     *
     * @param code 页面编码
     * @return 是否成功
     */
    Boolean deleteByCode(String code);

    /**
     * 通过页面编码批量删除页面
     *
     * @param codeList 页面编码
     * @return 是否成功
     */
    Boolean deleteByCode(List<String> codeList);

    /**
     * 判断页面是否存在
     *
     * @param code 页面编码
     * @return 是否存在
     */
    boolean exists(String code);

    /**
     * 获取页面列表
     *
     * @param appCode 应用编码
     * @return 页面列表
     */

    List<ZdPageDTO> listTree(String appCode, Integer type);

    /**
     * 获取页面默认表单code
     *
     * @param appCode 应用编码
     * @return 页面默认表单code
     */
    String getDefaultFormCode(String appCode);

    /**
     * 查询某个应用下的所有表单
     *
     * @param appCode 应用编码，用于查询对应应用的所有表单
     * @return 查询某个应用下的所有表单
     */
    List<Map<String, String>> getAllForm(String appCode);

    /**
     * 移动页面
     *
     * @param moveId   移动页面的id
     * @param targetId 基准节点id
     * @param type     移动方向（2：基准节点下级，1：基准节点前方，0：基准节点后方）
     * @return 移动后的页面
     */
    List<ZdPageDTO> move(Long moveId, Long targetId, Integer type);

    /**
     * 通过页面编码获取应用编码
     *
     * @param pageCodeList 页面编码
     * @return 应用编码
     */
    String getAppCodeByCode(List<String> pageCodeList);

    /**
     * 通过页面id获取应用编码
     *
     * @param id 页面id
     * @return 应用编码
     */
    String getAppCodeById(Long id);

    /**
     * 保存页面自定义url
     *
     * @param pageCustomUrlList 页面自定义url
     * @param orgId             人员id
     */
    void savePageCustomUrl(List<ZdPageCustomUrlDTO> pageCustomUrlList, String orgId);

    /**
     * 通过应用编码获取分组列表
     *
     * @param appCode 应用编码
     * @return 分组列表
     */
    List<OptionDTO<Long>> groupList(String appCode);

    List<ZdPageDTO> groupTree(String appCode);

    void updateCodeVal();


}
