package com.sinitek.sirm.nocode.form.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单按钮支持详情DTO")

public class ZdFormButtonsSupportDTO {
    @JsonIgnore
    private List<OperationAuthEnum> operationAuthEnums = new ArrayList<>();


    public ZdFormButtonsSupportDTO() {
    }

    public ZdFormButtonsSupportDTO(List<OperationAuthEnum> operationAuthEnums) {
        this.operationAuthEnums = operationAuthEnums;
        if (Objects.isNull(operationAuthEnums)) {
            this.operationAuthEnums = new ArrayList<>();
        }
    }


    @ApiModelProperty(value = "是否支持新增按钮", example = "true")
    public Boolean getShowAdd() {
        return operationAuthEnums.contains(OperationAuthEnum.EDIT);
    }

    @ApiModelProperty(value = "是否支持删除按钮", example = "true")
    public Boolean getShowDelete() {
        return operationAuthEnums.contains(OperationAuthEnum.DELETE);
    }

    @ApiModelProperty(value = "是否支持详情按钮", example = "false")
    public Boolean getShowView() {
        return operationAuthEnums.contains(OperationAuthEnum.VIEW);
    }
}
