package com.sinitek.sirm.nocode.common.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */
@Getter
public enum StatusEnum implements BaseIntegerEnum {
    ALL(null, "全部状态"),
    ENABLE(1, "已启用"),
    DISABLE(0, "未启用");

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    StatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static StatusEnum fromValue(Integer value) {
        return Arrays.stream(StatusEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }


}
