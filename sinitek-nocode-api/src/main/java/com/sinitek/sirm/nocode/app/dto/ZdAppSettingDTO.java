package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.page.dto.ZdPageCustomUrlDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "应用设置对象")
@Data
public class ZdAppSettingDTO {
    @ApiModelProperty(value = "应用对象",required = true)
    private ZdAppDTO app;
    @ApiModelProperty(value = "应用管理者数组")
    private List<ZdAppManagerDTO> appManagerList;
    @ApiModelProperty(value = "自定义页面地址")
    private List<ZdPageCustomUrlDTO> pageCustomUrlList;
    @ApiModelProperty(value = "应用OA集成")
    private ZdAppOaIntegrationDTO appOaIntegration;
    @ApiModelProperty(value = "应用页面地址")
    private ZdAppCustomUrlDTO pageUrl;
}
