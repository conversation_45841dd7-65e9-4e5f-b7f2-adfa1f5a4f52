package com.sinitek.sirm.nocode.form.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.OrderItemDTO;
import com.sinitek.sirm.nocode.form.enumerate.LogicOperatorEnum;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单数据搜索参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdFormDataSearchParamDTO extends PageDataParam implements FormCodeSupplier {
    @ApiModelProperty("搜索的关键字")
    private String allText;

    /**
     * 逻辑运算符
     */
    @ApiEnumProperty
    private LogicOperatorEnum logicOperator;

    @ApiModelProperty(value = "表单编码", required = true)
    @NotBlank(message = "表单编码不能为空")
    private String formCode;

    @ApiModelProperty("搜索字段,支持数据库本身字段查询(自带字段，有四个(字段描述，字段名称，值类型):" +
            "<br/>主键，_FDC_id，int" +
            "<br/>创建人id，_FDC_creator_id，text" +
            "<br/>创建时间，_FDC_createtimestamp，timestamp" +
            "<br/>修改时间，_FDC_updatetimestamp，timestamp"
    )
    private List<ZdFormDataSearchFieldDTO> searchField;


    @ApiModelProperty("排序字段,数组，支持多个字段排序(用于本身字段排序，有四个(id:主键,creator_id:创建人id,createtimestamp:创建时间,updatetimestamp:修改时间)")
    private List<OrderItemDTO> orderItemList;

    @ApiModelProperty("排序字段,数组，支持多个字段排序(用于自定义字段排序)")
    private List<CustomOrderItemDTO> customOrderItemList;

    @ApiModelProperty(value = "收集表日期", example = "2025-05-26")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate reportDate;
    /**
     * 所属权限
     */
    @ApiModelProperty("所属权限")
    @JsonIgnore
    private ZdPageAuthDTO zdPageAuthDTO;

    @JsonIgnore
    @ApiModelProperty(value = "当前登陆人orgId", required = true, example = "999999001")
    private String currentOrgId;
}
