package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 2025.0701
 * @since 1.0.0-SNAPSHOT
 */

@ApiModel(description = "变量规则值枚举")
@Getter
public enum VariableRuleValueEnum implements BaseStringEnum {
    FIXED("${loginUser}", "当前登陆人"),
    VARIABLE("${departments}", "当前登陆部门");


    @JsonValue
    @ApiModelProperty("值")
    private final String value;
    @ApiModelProperty("中文名称")
    private final String label;

    VariableRuleValueEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static VariableRuleValueEnum fromValue(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return Arrays.stream(VariableRuleValueEnum.values()).filter(a -> value.equalsIgnoreCase(a.value)).findAny().orElse(null);
    }
}
