package com.sinitek.sirm.nocode.common.utils;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 实体和类操作工具
 *
 * <AUTHOR>
 * @version 2025.0331
 * @since 1.0.0-SNAPSHOT
 */

@Slf4j
public class BeanUtilsEx {
    private static final int INIT_SIZE = 8;

    private BeanUtilsEx() {

    }

    /**
     * 设置不为空的数据
     *
     * @param supplier 数据提供者
     * @param consumer 数据消费者
     * @param <T>      数据泛型
     */
    public static <T> void setNotNullValue(Supplier<T> supplier, Consumer<T> consumer) {
        T t = supplier.get();
        if (StringUtils.checkValNotNull(t)) {
            consumer.accept(t);
        }
    }

    /**
     * 根据 属性值去重
     *
     * @param keyExtractor 定义的key
     * @param <T>          泛型
     * @return Predicate
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>(INIT_SIZE);
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }


}
