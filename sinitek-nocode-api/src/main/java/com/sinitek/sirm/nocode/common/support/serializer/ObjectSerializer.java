package com.sinitek.sirm.nocode.common.support.serializer;

import org.apache.commons.codec.binary.Base64;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2025.0409
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class ObjectSerializer implements RedisSerializer<Object> {
    @Override
    public byte[] serialize(Object object) throws SerializationException {
        byte[] result = new byte[0];
        if (object == null) {
            return result;
        } else {
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream(128);
            if (!(object instanceof Serializable)) {
                throw new SerializationException("requires a Serializable payload but received an object of type [" + object.getClass().getName() + "]");
            } else {
                try {
                    ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteStream);
                    objectOutputStream.writeObject(object);
                    objectOutputStream.flush();
                    result = byteStream.toByteArray();
                    return result;
                } catch (IOException e) {
                    throw new SerializationException("serialize error, object=" + object, e);
                }
            }
        }
    }

    @Override
    public Object deserialize(byte[] bytes) throws SerializationException {
        Object result = null;
        if (bytes != null && bytes.length != 0) {
            try {
                ByteArrayInputStream byteStream = new ByteArrayInputStream(bytes);
                ObjectInputStream objectInputStream = new ObjectInputStream(byteStream);
                result = objectInputStream.readObject();
                return result;
            } catch (IOException | ClassNotFoundException e) {
                throw new SerializationException("deserialize error", e);
            }
        } else {
            return result;
        }
    }

    public String convert(Object object) {
        return Base64.encodeBase64String(serialize(object));
    }


    @SuppressWarnings("unchecked")
    public <T> T convert(String str) {
        return (T) deserialize(Base64.decodeBase64(str));
    }
}
