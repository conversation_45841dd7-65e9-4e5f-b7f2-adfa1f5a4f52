package com.sinitek.sirm.nocode.form.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单填写人员")
@Data
public class ZdPageFormDataFillerDTO {
    @ApiModelProperty(value = "表单的主键，当填报人员填写了数据，会有值", example = "999000001")
    private Long id;
    @ApiModelProperty(value = "数据填充人员id", example = "999000001")
    private String orgId;
    @ApiModelProperty(value = "数据填充人员名称", example = "管理员")
    private String orgName;
    @ApiModelProperty(value = "头像，base64格式，使用的时候: <img src=\"当前值\"", example = "data:image/png;base64,/9j/4AAQSkZJRgABAQAAAQABAAD")
    private String photo;
    @ApiModelProperty(value = "数据提交时间", example = "2025年03月25日 15:40")
    @JsonFormat(pattern = "yyyy年MM月dd日 HH:mm", timezone = "GMT+8")
    private Date submitTime;
}
