package com.sinitek.sirm.nocode.common.annotation;

import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import com.sinitek.sirm.nocode.common.support.swagger.SwaggerEnumMaker;
import com.sinitek.sirm.nocode.common.support.swagger.makes.DefaultEnumMaker;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 枚举字段属性
 *
 * <AUTHOR>
 * @version 2025.0529
 * @since 1.0.0-SNAPSHOT
 */

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiEnumProperty {
    // 接口文档上的显示的字段名称，不设置则使用field本来名称
    String name() default "";

    /**
     * 枚举值
     *
     * @return 枚举值
     */
    String description() default "";

    /**
     * A sample description for the property.
     */
    String example() default "";


    /**
     * 是否必填
     *
     * @return true or false
     */
    boolean required() default false;

    /**
     * 枚举类class
     *
     * @return 枚举类
     */
    Class<?> enumClazz() default BaseEnum.class;

    /**
     * 枚举类型
     *
     * @return 枚举类型
     */
    int type() default 0;

    /**
     * 是否显示枚举
     *
     * @return true or false
     */
    boolean viewEnum() default true;

    /**
     * 是否显示ApiModel注解说明
     *
     * @return true or false
     */
    boolean apiModel() default true;

    /**
     * 是否保持数据类型
     *
     * @return true or false
     */
    boolean keepDataType() default false;

    /**
     * 自定义枚举类型
     *
     * @return 默认值
     */
    Class<? extends SwaggerEnumMaker> customEnum() default DefaultEnumMaker.class;

    /**
     * 枚举值的key。例如：{@link BaseEnum#getValue()} key 是value
     *
     * @return key
     */
    String valueKey() default "value";

    String labelKey() default "label";


}

