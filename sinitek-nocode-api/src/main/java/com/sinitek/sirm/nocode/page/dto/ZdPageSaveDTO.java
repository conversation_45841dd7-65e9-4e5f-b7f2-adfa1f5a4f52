package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 2025.0422
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "新建页面（表单）DTO")
public class ZdPageSaveDTO extends ZdPageGroupSaveDTO {
    /**
     * 页面类型，有三种，0:普通表单，1：流程表单，2:报表表单,9：分组
     */
    @NotNull(message = "页面类型不能为空")
    @ApiEnumProperty(required = true, example = "0")
    private PageTypeEnum pageType;
}
