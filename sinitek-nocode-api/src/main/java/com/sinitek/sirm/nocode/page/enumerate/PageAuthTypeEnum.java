package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 页面权限类型枚举
 *
 * <AUTHOR>
 * @version 2025.0327
 * @since 1.0.0-SNAPSHOT
 */
@Getter
public enum PageAuthTypeEnum implements BaseIntegerEnum {
    SUBMIT_AUTH(0, "提交权限"),
    DATA_AUTH(1, "数据权限");

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    PageAuthTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static PageAuthTypeEnum fromValue(Integer value) {
        return Arrays.stream(PageAuthTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }

    /**
     * 兼容字符串
     *
     * @param value 字符串值
     * @return 枚举值
     */
    @JsonCreator
    public static PageAuthTypeEnum fromValue(String value) {
        return fromValue(NumberTool.safeToInteger(value, -1));
    }
}
