package com.sinitek.sirm.nocode.page.service;

import com.sinitek.sirm.common.dto.OptionDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSceneDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-12 14:21:01
 * @description 针对表【zd_page_scene(页面场景设置)】的数据库操作Service
 */
public interface IZdPageSceneService {
    /**
     * 保存或者修改场景
     *
     * @param pageSceneSaveDTO 参数
     * @return 是否成功
     */
    boolean saveOrUpdate(ZdPageSceneDTO pageSceneSaveDTO);

    /**
     * 通过页面code获取场景
     *
     * @param pageCode 页面code
     * @return 场景
     */
    ZdPageSceneDTO getPageScene(String pageCode);

    /**
     * 获取场景类型列表
     * 只有提交范围的权限才有收集表单
     *
     * @param pageCode 页面code
     * @return 场景类型列表
     */
    List<OptionDTO<Integer>> sceneTypeList(String pageCode);

}
