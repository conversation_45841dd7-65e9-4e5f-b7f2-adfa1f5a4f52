package com.sinitek.sirm.nocode.page.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class ZdPageCustomUrlDTO {
    /**
     * 自定义的页面地址
     */
    @NotBlank(message = "自定义的页面地址不能为空")
    @ApiModelProperty(value = "自定义的页面地址", example = "jicode123456", required = true)
    private String url;

    /**
     * 页面编码
     */
    @NotBlank(message = "表单编不能为空")
    @ApiModelProperty(value = "表单编码", example = "page_57049cf9c4e94acfbc94d6775bc9e73e", required = true)
    private String code;
}
