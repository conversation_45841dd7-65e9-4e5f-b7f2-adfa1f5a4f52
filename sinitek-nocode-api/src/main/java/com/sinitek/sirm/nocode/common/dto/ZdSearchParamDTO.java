package com.sinitek.sirm.nocode.common.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0318
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "查询条件")
@EqualsAndHashCode(callSuper = true)
public class ZdSearchParamDTO extends PageDataParam {
    @ApiModelProperty(value = "名称", required = false, example = "报销系统")
    private String name;

}
