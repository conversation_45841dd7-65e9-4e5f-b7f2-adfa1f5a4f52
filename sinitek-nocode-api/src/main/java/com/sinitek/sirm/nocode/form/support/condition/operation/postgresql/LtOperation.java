package com.sinitek.sirm.nocode.form.support.condition.operation.postgresql;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.support.condition.OperationInterface;

/**
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */

public class LtOperation implements OperationInterface {
    @Override
    public void apply(QueryWrapper<?> wrapper, String fieldName, Object value) {
        String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_LT, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, OperationInterface.jsonb(value));
    }

    @Override
    public OperatorEnum type() {
        return OperatorEnum.LT;
    }
}
