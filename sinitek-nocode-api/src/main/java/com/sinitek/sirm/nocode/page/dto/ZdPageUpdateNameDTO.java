package com.sinitek.sirm.nocode.page.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0422
 * @description 页面修改名称 参数
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "页面修改名称DTO")
@Data
public class ZdPageUpdateNameDTO {
    @ApiModelProperty(value = "页面编码", required = true, example = "page_114b522908e04cd09deeea7f4c25ca25")
    @NotBlank(message = "页面编码不能为空")
    private String code;

    @ApiModelProperty(value = "页面名称", required = true, example = "我的请假")
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称不能超过100个字符")
    private String name;
}
