package com.sinitek.sirm.nocode.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0311
 * @since 1.0.0-SNAPSHOT
 */
@SuppressWarnings({"unchecked"})
public class ConvertUtil {
    private ConvertUtil(){}
    public static <T,E extends T> IPage<T> page(Page<E> page){
        return (IPage<T>) page;
    }

    /**
     * list 转换器
     * @param list list
     * @return 转换后的list
     * @param <A> 泛型a
     * @param <B> 泛型b
     */
    public static <A,B> List<B> list(List<A> list){
        return (List<B>) list;
    }

}
