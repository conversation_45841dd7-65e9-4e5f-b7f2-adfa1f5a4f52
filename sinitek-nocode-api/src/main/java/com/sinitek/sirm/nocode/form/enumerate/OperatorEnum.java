package com.sinitek.sirm.nocode.form.enumerate;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.interfaces.Compare;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0313
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "操作枚举")
@Getter
public enum OperatorEnum implements BaseStringEnum {


    @ApiModelProperty("等于")
    EQ(SqlKeyword.EQ.name().toLowerCase(), "等于", "equal", "1,2", Compare::eq, null, (wrapper, fieldName, value) -> {
        String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_EQ, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, jsonb(value));
    }),
    @ApiModelProperty("不等于")
    NE(SqlKeyword.NE.name().toLowerCase(), "不等于", "not equal", "1,2", Compare::ne, null, (wrapper, fieldName, value) -> {
        String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_NOT_EQ, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, jsonb(value));
    }),
    @ApiModelProperty("大于")
    GT(SqlKeyword.GT.name().toLowerCase(), "大于", "greater than", null, Compare::gt, null, (wrapper, fieldName, value) -> {
        String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_GT, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, jsonb(value));
    }),
    @ApiModelProperty("大于等于")
    GE(SqlKeyword.GE.name().toLowerCase(), "大于等于", "greater or equal", "3", Compare::ge, null, (wrapper, fieldName, value) -> {
        String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_GE, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, jsonb(value));
    }),
    @ApiModelProperty("小于")
    LT(SqlKeyword.LT.name().toLowerCase(), "小于", "less than", null, Compare::lt, null, (wrapper, fieldName, value) -> {
        String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_LT, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, jsonb(value));
    }),
    @ApiModelProperty("小于等于")
    LE(SqlKeyword.LE.name().toLowerCase(), "小于等于", "less or equal", "3", Compare::le, null, (wrapper, fieldName, value) -> {
        String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_LE, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, jsonb(value));
    }),
    @ApiModelProperty("介于")
    BETWEEN(SqlKeyword.BETWEEN.name().toLowerCase(), "介于", "between", null, (a, b, c) -> {
        Pair<Object, Object> pairValue = pairValue(c);
        if (Objects.nonNull(pairValue)) {
            a.between(b, pairValue.getKey(), pairValue.getValue());
        }
    }, List.class, (wrapper, fieldName, value) -> {
        Pair<Object, Object> pairValue = pairValue(value);
        if (Objects.nonNull(pairValue)) {
            String eq = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_BETWEEN, fieldName);
            wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, eq, betweenJsonb(pairValue));
        }

    }),
    @ApiModelProperty("为空")
    IS_NULL(SqlKeyword.IS_NULL.name().toLowerCase(), "为空", "is null", null, (a, b, c) -> a.isNull(b), null, (wrapper, fieldName, value) -> {
        String isnull = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_IS_NULL, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, isnull, "{}");
    }),
    @ApiModelProperty("不为空")
    IS_NOT_NULL(SqlKeyword.IS_NOT_NULL.name().toLowerCase(), "不为空", "is not null", null, (a, b, c) -> a.isNotNull(b), null, (wrapper, fieldName, value) -> {
        String notnull = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_NOT_NULL, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, notnull, "{}");
    }),
    @ApiModelProperty("等于任意一个")
    IN(SqlKeyword.IN.name().toLowerCase(), "包含", "in any", null, (wrapper, fieldName, value) -> {
        if (Objects.isNull(value)) {
            return;
        }
        if (value.getClass().isArray()) {
            Object[] objects = (Object[]) value;
            wrapper.in(objects.length > 0, fieldName, objects);
        } else if (List.class.isAssignableFrom(value.getClass())) {
            List<?> list = (List<?>) value;
            wrapper.in(!list.isEmpty(), fieldName, list);
        } else {
            wrapper.eq(fieldName, value);
        }
    }, List.class, (wrapper, fieldName, value) -> {
        String in = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_IN, fieldName);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, in, jsonb(value));
    }),
    @ApiModelProperty("不等于任意一个")
    NOT_IN(SqlKeyword.NOT_IN.name().toLowerCase(), "不包含", "not in any", "1", (wrapper, fieldName, value) -> {
        if (Objects.isNull(value)) {
            return;
        }
        if (value.getClass().isArray()) {
            Object[] objects = (Object[]) value;
            wrapper.notIn(objects.length > 0, fieldName, objects);
        } else if (List.class.isAssignableFrom(value.getClass())) {
            List<?> list = (List<?>) value;
            wrapper.notIn(!list.isEmpty(), fieldName, list);
        } else {
            wrapper.ne(fieldName, value);
        }
    }, List.class, null),
    LIKE(SqlKeyword.LIKE.name().toLowerCase(), "像", "like", null, Compare::like, String.class, (wrapper, fieldName, value) -> {
        String like = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_LIKE, fieldName, value);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, like);
    }),
    NOT_LIKE(SqlKeyword.NOT_LIKE.name().toLowerCase(), "不像", "not like", null, Compare::notLike, String.class, (wrapper, fieldName, value) -> {
        String like = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_NOT_LIKE, fieldName, value);
        wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS, like);
    });


    @JsonValue
    @ApiModelProperty("值")
    private final String value;
    @ApiModelProperty("中文名称")
    private final String label;
    @ApiModelProperty("英文名称")
    private final String labelEn;


    @ApiModelProperty("所属")
    private final String ownedType;

    @ApiModelProperty("操作动作")
    @JsonIgnore
    @Getter(AccessLevel.NONE)
    private final OperatorInterface op;

    @ApiModelProperty("数值类型")
    @JsonIgnore
    @Getter(AccessLevel.NONE)
    private final Class<?> rawType;

    @ApiModelProperty("jsonb_path_exists函数")
    @JsonIgnore
    @Getter(AccessLevel.NONE)
    private final OperatorInterface jsonPathExists;


    OperatorEnum(String value, String label, String labelEn, String ownedType, OperatorInterface op, Class<?> rawType, OperatorInterface jsonPathExists) {
        this.value = value;
        this.label = label;
        this.labelEn = labelEn;
        this.ownedType = ownedType;
        this.op = op;
        this.rawType = rawType;
        this.jsonPathExists = jsonPathExists;
    }

    public static List<OperatorEnum> list() {
        return new ArrayList<>(Arrays.asList(OperatorEnum.values()));
    }

    @JsonCreator
    public static OperatorEnum fromValue(String value) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (value.startsWith("{")) {
            value = (String) JsonUtil.toMap(value).get("description");
        }
        String finalValue = value;
        return Arrays.stream(OperatorEnum.values()).filter(a -> Objects.equals(finalValue, a.getValue())).findAny().orElse(null);
    }

    /**
     * jsonb格式化
     *
     * @param object 值
     * @return 格式化后的值
     */
    private static String jsonb(Object object) {
        String value = formatValue(object);
        return String.format(ZdPgSqlConstant.OBJECT_VALUES, value);
    }

    /**
     * jsonb格式化,用于 between 操作
     *
     * @param pair 值
     * @return 格式化后的值
     */
    private static String betweenJsonb(Pair<Object, Object> pair) {
        return String.format("{\"min\":%s,\"max\":%s}", formatValue(pair.getKey()), formatValue(pair.getValue()));
    }

    /**
     * 格式化值
     *
     * @param value 值
     * @return 格式化后的值
     */
    private static String formatValue(Object value) {
        if (value instanceof String) {
            return String.format(ZdPgSqlConstant.QT_FORMAT, value);
        }
        if (value instanceof List) {
            String start = "[";
            String end = "]";
            return start + ((List<?>) value).stream().map(OperatorEnum::formatValue)
                    .collect(Collectors.joining(",")) + end;
        }
        if (value.getClass().isArray()) {
            return formatValue(Arrays.stream((Object[]) value).collect(Collectors.toList()));
        }
        return String.valueOf(value);
    }

    /**
     * 获取pair
     *
     * @param value 值
     * @return pair
     */
    private static Pair<Object, Object> pairValue(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (value.getClass().isArray()) {
            Object[] objects = (Object[]) value;
            if (objects.length == 2) {
                return Pair.of(objects[0], objects[1]);
            }
        } else if (List.class.isAssignableFrom(value.getClass())) {
            List<?> list = (List<?>) value;
            if (list.size() == 2) {
                return Pair.of(list.get(0), list.get(1));
            }
        }
        return null;
    }

    @Override
    public int findTypeWithTarget(int type) {
        if (Objects.nonNull(ownedType) && ownedType.contains(String.valueOf(type))) {
            return type;

        }
        return getType();
    }

    /**
     * 操作
     *
     * @param type      值的类型
     * @param wrapper   wrap
     * @param fieldName 字段名
     * @param value     值
     */

    public void apply(Class<?> type, QueryWrapper<?> wrapper, String fieldName, Object value) {
        value = getObject(type, value);
        op.apply(wrapper, fieldName, value);
    }

    /**
     * jsonb_path_exists函数
     *
     * @param type      值的类型
     * @param wrapper   wrap
     * @param fieldName 字段名
     * @param value     值
     */
    public void jsonPathExists(Class<?> type, QueryWrapper<?> wrapper, String fieldName, Object value) {
        value = getObject(type, value);
        jsonPathExists.apply(wrapper, fieldName, value);
    }


    public Object getObject(Class<?> type, Object value) {
        if (Objects.nonNull(type)) {
            JsonbTypeHandler<?> handler;
            if (Objects.nonNull(rawType)) {
                if (Objects.equals(rawType, String.class)) {
                    handler = JsonbTypeHandler.ins(String.class);
                } else {
                    handler = JsonbTypeHandler.ins(rawType, type);
                }
            } else {
                handler = JsonbTypeHandler.ins(type);
            }
            value = handler.parseObject(value);
        }
        return value;
    }

    @FunctionalInterface
    private interface OperatorInterface {
        /**
         * 操作
         *
         * @param wrapper   wrap
         * @param fieldName 字段名
         * @param value     值
         */
        void apply(QueryWrapper<?> wrapper, String fieldName, Object value);
    }


}
