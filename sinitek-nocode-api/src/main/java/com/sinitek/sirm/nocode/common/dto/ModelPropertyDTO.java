package com.sinitek.sirm.nocode.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0428
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "模型属性对象")
public class ModelPropertyDTO {
    @ApiModelProperty(value = "属性名称", example = "name", required = true)
    private String name;
    @ApiModelProperty(value = "是否是必须的", example = "true", required = true)
    private Boolean required;
    @ApiModelProperty(value = "属性描述", example = "名字", required = true)
    private String description;
    @ApiModelProperty(value = "举例")
    private String example;
}
