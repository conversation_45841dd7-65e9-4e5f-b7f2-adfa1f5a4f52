package com.sinitek.sirm.nocode.common.support.enumeratebase;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sinitek.sirm.common.dto.OptionDTO;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */

public interface BaseEnum<T extends Serializable> extends IEnum<T> {
    Logger log = LoggerFactory.getLogger(BaseEnum.class);

    /**
     * 获取枚举名称
     *
     * @return 枚举名称
     */
    String getLabel();

    /**
     * 获取枚举描述
     *
     * @return 枚举描述
     */
    default String getDesc() {
        return null;
    }

    /**
     * 获取枚举类型,默认是0
     *
     * @return 枚举类型
     */
    default int getType() {
        return 0;
    }

    /**
     * 获取枚举类型,默认是0
     *
     * @return 枚举类型
     */
    default int findTypeWithTarget(int targetType) {
        return getType();
    }

    /**
     * 是否启用
     *
     * @return 是否启用
     */
    default boolean isEnable() {
        return true;
    }

    /**
     * 获取成员bean名称
     *
     * @return bean的名称
     */
    @SuppressWarnings("unchecked")
    default <E extends Enum<E>> String beanName(String suffix) {
        return StringUtils.underlineToCamel(((E) this).name()) + suffix;
    }

    @SuppressWarnings("unchecked")
    static <T extends Serializable> List<OptionDTO<T>> option(Class<?> aClass, int type) {
        if (BaseEnum.class.isAssignableFrom(aClass) && aClass.isEnum()) {
            BaseEnum<T>[] enumConstants = (BaseEnum<T>[]) aClass.getEnumConstants();
            return Arrays.stream(enumConstants).filter(e -> e.findTypeWithTarget(type) == type && e.isEnable()).map(e -> new OptionDTO<>(e.getLabel(), e.getValue())).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    static <T extends Serializable> List<OptionDTO<T>> option(Class<?> aClass) {
        return option(aClass, 0);
    }

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */

    static <T extends Serializable> List<OptionDTO<T>> option(String modelName, String enumName, int type) {
        // 类首字母大写
        enumName = CharSequenceUtil.upperFirst(enumName);
        String classPath = String.format(ZdCommonConstant.ENUMERATE_CLASS, modelName, enumName);
        List<OptionDTO<T>> list = new ArrayList<>();
        try {
            Class<?> aClass = Class.forName(classPath);
            return option(aClass, type);
        } catch (Exception e) {
            log.error("获取枚举值异常:{}", e.getMessage(), e);
            return list;
        }
    }

    /**
     * 组装map
     *
     * @param cClass 子类
     * @param <T>    value泛型
     * @param <C>    children 泛型
     * @return map
     */
    static <T extends Serializable, C extends BaseEnum<T>> Map<T, C> map(Class<C> cClass) {
        Map<T, C> map = new LinkedHashMap<>();
        if (cClass.isEnum()) {
            C[] enumConstants = cClass.getEnumConstants();
            for (C c : enumConstants) {
                map.put(c.getValue(), c);
            }
        }
        return map;
    }

    /**
     * 组装list
     *
     * @param cClass 枚举类
     * @param <T>    value泛型
     * @param <C>    children 泛型
     * @return list
     */
    static <T extends Serializable, C extends BaseEnum<T>> List<C> list(Class<C> cClass, int type) {
        return map(cClass).values().stream().filter(e -> e.findTypeWithTarget(type) == type && e.isEnable()).collect(Collectors.toList());
    }
}
