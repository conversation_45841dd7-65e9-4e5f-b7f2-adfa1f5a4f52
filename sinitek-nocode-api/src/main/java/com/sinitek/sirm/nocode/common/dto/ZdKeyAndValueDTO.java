package com.sinitek.sirm.nocode.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0624
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "键值对DTO")
public class ZdKeyAndValueDTO {
    @ApiModelProperty(value = "唯一标志", example = "name", required = true)
    private String key;
    @ApiModelProperty(value = "值", example = "张三", required = true)
    private Object value;
}
