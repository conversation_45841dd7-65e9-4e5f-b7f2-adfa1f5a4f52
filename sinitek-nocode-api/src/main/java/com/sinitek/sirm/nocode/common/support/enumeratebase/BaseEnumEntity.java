package com.sinitek.sirm.nocode.common.support.enumeratebase;

import lombok.Data;

import java.io.Serializable;

/**
 * 枚举实体
 *
 * <AUTHOR>
 * @version 2025.0531
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class BaseEnumEntity implements BaseEnum<Serializable> {
    /**
     * 值
     */
    private Serializable value;
    /**
     * 标签
     */
    private String label;
    /**
     * 描述
     */
    private String desc;
    /**
     * 类型
     */
    private int type;
}
