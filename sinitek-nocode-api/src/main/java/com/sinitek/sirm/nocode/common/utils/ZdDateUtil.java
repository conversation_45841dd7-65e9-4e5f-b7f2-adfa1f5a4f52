package com.sinitek.sirm.nocode.common.utils;

import cn.hutool.core.lang.Pair;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @version 2025.0515
 * @since 1.0.0-SNAPSHOT
 */

public class ZdDateUtil {
    private static final int WEEK_DATES = 6;

    private ZdDateUtil() {
    }

    /**
     * 获取指定日期的开始时间和结束时间
     *
     * @param date 指定日期
     * @return Pair<LocalDateTime, LocalDateTime>
     */
    public static Pair<LocalDateTime, LocalDateTime> day(LocalDate date) {
        if (Objects.isNull(date)) {
            date = LocalDate.now();
        }
        // 当天开始时间（00:00:00）
        LocalDateTime startOfDay = date.atStartOfDay();
        // 当天结束时间（23:59:59.999999999）
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);
        return Pair.of(startOfDay, endOfDay);
    }

    /**
     * 获取指定日期的当周开始时间和结束时间
     *
     * @param date 指定日期
     * @return Pair<LocalDateTime, LocalDateTime>
     */
    public static Pair<LocalDateTime, LocalDateTime> week(LocalDate date) {
        if (Objects.isNull(date)) {
            date = LocalDate.now();
        }
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        DayOfWeek firstDayOfWeek = weekFields.getFirstDayOfWeek();

        // 计算本周开始时间（当天的 00:00:00）
        LocalDate startOfWeekDate = date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));
        LocalDateTime startOfWeek = startOfWeekDate.atStartOfDay();

        // 计算本周结束时间（最后一天的 23:59:59.999999999）
        LocalDate endOfWeekDate = startOfWeekDate.plusDays(WEEK_DATES);
        LocalDateTime endOfWeek = endOfWeekDate.atTime(LocalTime.MAX);
        return Pair.of(startOfWeek, endOfWeek);
    }

    /**
     * 获取指定日期的当月开始时间和结束时间
     *
     * @param date 指定日期
     * @return Pair<LocalDateTime, LocalDateTime>
     */
    public static Pair<LocalDateTime, LocalDateTime> month(LocalDate date) {
        if (Objects.isNull(date)) {
            date = LocalDate.now();
        }

        // 当月开始时间（首日 00:00:00）
        LocalDateTime startOfMonth = date
                .with(TemporalAdjusters.firstDayOfMonth())
                .atStartOfDay();
        // 当月结束时间（最后一日 23:59:59.999999999）
        LocalDateTime endOfMonth = date
                .with(TemporalAdjusters.lastDayOfMonth())
                .atTime(LocalTime.MAX);
        return Pair.of(startOfMonth, endOfMonth);
    }


    /**
     * 时间类型转化
     *
     * @param localDateTime 时间
     * @return Date
     */
    public static Date toDate(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 时间类型转化
     *
     * @param localDate 时间
     * @return Date
     */
    public static Date toDate(LocalDate localDate) {
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 时间类型转化
     *
     * @param date 日期
     * @return 字符串日期
     */

    public static String format(LocalDate date, String format) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return dateTimeFormatter.format(date);
    }

    /**
     * 时间类型转化
     *
     * @param date 日期
     * @return 字符串日期
     */

    public static LocalDate format(String date, String format) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return LocalDate.parse(date, dateTimeFormatter);
    }
}
