package com.sinitek.sirm.nocode.form.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单数据显示设置
 *
 * @TableName zd_page_form_show_config
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "表单数据显示DTO")
public class ZdPageFormShowConfigDTO extends ZdIdDTO {

    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码")
    private String formCode;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String orgId;

    /**
     * 是个json对象，例如:[{key:name,showOrder:0}]
     */
    @ApiModelProperty(value = "显示配置，是个json对象")
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String showConfig;
}
