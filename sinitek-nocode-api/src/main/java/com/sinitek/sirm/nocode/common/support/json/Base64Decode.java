package com.sinitek.sirm.nocode.common.support.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.sinitek.sirm.common.utils.Base64Utils;

import java.io.IOException;

/**
 * base64解密，用于反序列化
 *
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
public class Base64Decode extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String str;
        if (jsonParser == null || (str = jsonParser.getText()) == null) {
            return null;
        }
        return Base64Utils.decode(str);
    }
}
