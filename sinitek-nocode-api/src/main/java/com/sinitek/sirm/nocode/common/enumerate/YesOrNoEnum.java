package com.sinitek.sirm.nocode.common.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0418
 * @description 是否枚举
 * @since 1.0.0-SNAPSHOT
 */
@Getter
public enum YesOrNoEnum implements BaseIntegerEnum {
    YES(1, "是"),
    NO(0, "否");;

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    YesOrNoEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static YesOrNoEnum fromValue(Integer value) {
        return Arrays.stream(YesOrNoEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
