package com.sinitek.sirm.nocode.common.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sinitek.data.model.tree.entity.BaseNodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0318
 * @since 1.0.0-SNAPSHOT
 */
@Setter
@Getter
@ApiModel(description = "树节点")
public class ZdBaseNodeEntity extends BaseNodeEntity {
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称不能超过100个字符")
    @ApiModelProperty(value = "名称", example = "我的简历")
    private String name;


    @TableField(exist = false)
    @ApiModelProperty(value = "子节点")
    private List<? extends ZdBaseNodeEntity> children;

}
