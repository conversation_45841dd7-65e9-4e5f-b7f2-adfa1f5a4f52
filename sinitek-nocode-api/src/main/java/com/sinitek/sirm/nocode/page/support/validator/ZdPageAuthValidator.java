package com.sinitek.sirm.nocode.page.support.validator;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.nocode.common.support.validator.base.IValidator;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataScopeCustomConditionDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.enumerate.DataScopeEnum;
import com.sinitek.sirm.nocode.page.enumerate.MemberTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0414
 * @description 权限验证
 * @since 1.0.0-SNAPSHOT
 */
@Component
@RequiredArgsConstructor
public class ZdPageAuthValidator implements IValidator<ZdPageAuthDTO> {
    private final IZdPageService pageService;

    @Override
    public String valid(ZdPageAuthDTO zdPageAuthDTO) {
        String pageCode = zdPageAuthDTO.getPageCode();
        if (!pageService.exists(pageCode)) {
            return "页面编码不存在";
        }
        MemberTypeEnum memberType = zdPageAuthDTO.getMemberType();
        List<String> memberOrgIds = zdPageAuthDTO.getMemberOrgIdList();
        if (!Objects.equals(MemberTypeEnum.ALL, memberType) && CollectionUtils.isEmpty(memberOrgIds)) {
            return "成员类型不为所有成员时，成员id不能为空";
        }
        PageAuthTypeEnum authType = zdPageAuthDTO.getAuthType();
        // 数据查看时，需要数据范围
        if (Objects.equals(authType, PageAuthTypeEnum.DATA_AUTH)) {
            List<String> dataScopeList = zdPageAuthDTO.getDataScopeList();
            if (CollectionUtils.isEmpty(dataScopeList)) {
                return "数据范围不能为空";
            }
            if (dataScopeList.contains(DataScopeEnum.CUSTOM_FILTER.getValue())) {
                // 自定义数据范围
                ZdFormDataScopeCustomConditionDTO customDataScope = zdPageAuthDTO.getCustomDataScope();
                if (Objects.isNull(customDataScope)) {
                    return "自定义过滤条件不能为空";
                }
            }
            if (dataScopeList.contains(DataScopeEnum.CUSTOM_DEPARTMENT.getValue())) {
                List<String> departmentIdList = zdPageAuthDTO.getDepartmentIdList();
                if (CollectionUtils.isEmpty(departmentIdList)) {
                    return "数据范围包含自定义部门时，自定义部门数据不能为空";
                }
            }
        }
        return null;
    }
}
