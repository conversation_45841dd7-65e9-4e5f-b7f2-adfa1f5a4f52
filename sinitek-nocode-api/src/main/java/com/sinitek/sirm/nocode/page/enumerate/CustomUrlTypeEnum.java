package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2025.0617
 * @since 1.0.0-SNAPSHOT
 */
@Getter
public enum CustomUrlTypeEnum implements BaseStringEnum {
    APP("app", "应用"),
    FORM("form", "表单"),

    ;
    /**
     * 值
     */
    @JsonValue
    private final String value;
    /**
     * 名称
     */
    private final String label;

    CustomUrlTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
