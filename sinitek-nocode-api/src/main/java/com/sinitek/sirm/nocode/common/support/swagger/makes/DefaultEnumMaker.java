package com.sinitek.sirm.nocode.common.support.swagger.makes;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnumEntity;
import com.sinitek.sirm.nocode.common.support.swagger.SwaggerEnumMaker;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0530
 * @since 1.0.0-SNAPSHOT
 */

public class DefaultEnumMaker<T extends Enum<T>> implements SwaggerEnumMaker {
    @Override
    public List<? extends BaseEnum<?>> findEnums(ApiEnumProperty apiEnumProperty) {
        Class<?> clazz = apiEnumProperty.enumClazz();
        List<BaseEnumEntity> baseEnumEntityList = new ArrayList<>();

        boolean hasValueGetter = Objects.nonNull(BeanUtil.getPropertyDescriptor(clazz, apiEnumProperty.valueKey()));
        boolean hasLabelGetter = Objects.nonNull(BeanUtil.getPropertyDescriptor(clazz, apiEnumProperty.labelKey()));
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            Class<?> type = declaredField.getType();
            if (!Objects.equals(type, clazz)) {
                continue;
            }
            // 具体的枚举值
            Object enumObject = ReflectUtil.getFieldValue(clazz, declaredField);
            // 假如是枚举的话

            BaseEnumEntity baseEnumEntity = new BaseEnumEntity();
            Serializable value;
            if (hasValueGetter) {
                value = (Serializable) BeanUtil.getFieldValue(enumObject, apiEnumProperty.valueKey());
            } else {
                // 名称
                value = declaredField.getName();
            }
            String label = null;
            if (hasLabelGetter) {
                label = (String) BeanUtil.getFieldValue(enumObject, apiEnumProperty.labelKey());
            } else {
                ApiModelProperty annotation = declaredField.getAnnotation(ApiModelProperty.class);
                if (Objects.nonNull(annotation)) {
                    label = annotation.value();
                }
            }
            baseEnumEntity.setValue(value);
            baseEnumEntity.setLabel(label);
            baseEnumEntityList.add(baseEnumEntity);
        }


        return baseEnumEntityList;
    }
}
