package com.sinitek.sirm.nocode.form.support.condition.operation.postgresql;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.support.condition.OperationInterface;

/**
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */

public class GeOperation implements OperationInterface {
    @Override
    public void apply(QueryWrapper<?> wrapper, String fieldName, Object value) {

    }

    @Override
    public OperatorEnum type() {
        return OperatorEnum.GE;
    }
}
