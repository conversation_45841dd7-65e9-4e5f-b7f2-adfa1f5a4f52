package com.sinitek.sirm.nocode.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2020.1104
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(value = "树状结构VO")
public class ZdTreeDTO<T extends ZdBaseNodeEntity> {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Object id;
    /**
     * 显示名称，同name,text
     */
    @ApiModelProperty(value = "显示名称，同name,text")
    private String label;
    /**
     * 是否是不可用的
     */
    @ApiModelProperty(value = "是否是不可用的")
    private Boolean disabled;
    /**
     * 子集
     */
    @ApiModelProperty(value = "子集")
    private List<ZdTreeDTO<T>> children;
    /**
     * 原始数据
     */
    @ApiModelProperty(value = "原始数据")
    private T treeOrgData;
    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private Integer levelNumber;
    /**
     * 父级主键
     */
    @ApiModelProperty(value = "父级主键")
    private Object parentId;


}
