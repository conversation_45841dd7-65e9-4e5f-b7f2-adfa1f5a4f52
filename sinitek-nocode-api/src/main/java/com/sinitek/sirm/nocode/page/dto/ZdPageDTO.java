package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.ZdBaseNodeEntity;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 页面表
 *
 * @TableName zd_page
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "页面DTO")
public class ZdPageDTO extends ZdBaseNodeEntity {
    /**
     * 应用编码
     */
    @NotBlank(message = "应用编码不能为空")
    @ApiModelProperty(value = "应用编码", example = "app_493d587de0304f35af50ab9cd33ece9d")
    private String appCode;
    /**
     * 页面编码
     */
    @ApiModelProperty(value = "页面编码", example = "page_23dcc69621b342f4b46a33473c25f6b9")
    private String code;
    /**
     * 页面自定义地址
     */
    @ApiModelProperty(value = "页面自定义地址", example = "abc1000")
    private String url;
    /**
     * 页面类型
     */
    @NotNull(message = "页面类型不能为空")
    @ApiEnumProperty(required = true, example = "0")
    private PageTypeEnum pageType;
}
