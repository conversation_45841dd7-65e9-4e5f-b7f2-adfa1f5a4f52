package com.sinitek.sirm.nocode.form.service;

import java.util.Map;

/**
 * 表单配置表Service接口
 *
 * <AUTHOR>
 * @version 2025.0604
 * @description 针对表【zd_page_form_field_mapping(表单配置表)】的数据库操作Service
 * @since 1.0.0-SNAPSHOT
 */
public interface IZdPageFormFieldMappingService {


    /**
     * 根据表单编码和字段名称查询字段映射代码
     *
     * @param formCode 表单编码
     * @param name     字段名称
     * @return 字段映射代码
     */
    String getFieldMappingCode(String formCode, String name);

    Map<String, String> getFieldMappingCode(String formCode);

    /**
     * 保存或者更新字段映射
     *
     * @param formCode 表单编码
     * @param pageData 表单数据
     */
    void saveOrUpdate(String formCode, String pageData);
}
