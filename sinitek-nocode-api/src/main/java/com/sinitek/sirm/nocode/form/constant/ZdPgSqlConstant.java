package com.sinitek.sirm.nocode.form.constant;

/**
 * pg sql 常量
 *
 * <AUTHOR>
 * @version 2025.0610
 * @since 1.0.0-SNAPSHOT
 */

public class ZdPgSqlConstant {
    private ZdPgSqlConstant() {
    }

    /**
     * 数据库字段前缀
     */
    public static final String FIELD_PREFIX = "_FDC_";

    /**
     * 数组全部
     */
    public static final String ARRAY_ALL = "[*]";

    /**
     * 引号格式
     */
    public static final String QT_FORMAT = "\"%s\"";

    public static final String OBJECT_VALUES = "{\"values\":%s}";

    /**
     * jsonb_path_exists函数，第三个参数是配置
     */
    public static final String JSONB_PATH_EXISTS = "jsonb_path_exists(form_data,{0}::jsonpath,{1}::jsonb,true)";


    public static final String COLUMN_NAME = "$.%s ? (@";

    /**
     * jsonb_path_exists函数，like操作
     */
    public static final String JSONB_PATH_EXISTS_LIKE = COLUMN_NAME + " like_regex \"%s\" flag \"i\")";
    public static final String JSONB_PATH_EXISTS_NOT_LIKE = COLUMN_NAME + " ! like_regex \"%s\" flag \"i\")";
    /**
     * jsonb_path_exists函数，=操作
     */
    public static final String JSONB_PATH_EXISTS_EQ = COLUMN_NAME + " == $values)";
    /**
     * jsonb_path_exists函数，!=操作
     */
    public static final String JSONB_PATH_EXISTS_NOT_EQ = COLUMN_NAME + " != $values)";
    /**
     * jsonb_path_exists函数，>操作
     */
    public static final String JSONB_PATH_EXISTS_GT = COLUMN_NAME + " > $values)";
    /**
     * jsonb_path_exists函数，>=操作
     */
    public static final String JSONB_PATH_EXISTS_GE = COLUMN_NAME + " >= $values)";

    /**
     * jsonb_path_exists函数，<操作
     */
    public static final String JSONB_PATH_EXISTS_LT = COLUMN_NAME + " < $values)";
    /**
     * jsonb_path_exists函数，<=操作
     */
    public static final String JSONB_PATH_EXISTS_LE = COLUMN_NAME + " <= $values)";
    /**
     * jsonb_path_exists函数，between操作
     */
    public static final String JSONB_PATH_EXISTS_BETWEEN = COLUMN_NAME + " >= $min && @ <= $max)";
    /**
     * jsonb_path_exists函数，is null操作
     */
    public static final String JSONB_PATH_EXISTS_IS_NULL = COLUMN_NAME + " == null)";
    /**
     * jsonb_path_exists函数，is not null操作
     */
    public static final String JSONB_PATH_EXISTS_NOT_NULL = COLUMN_NAME + " != null)";
    /**
     * jsonb_path_exists函数，in操作
     */
    public static final String JSONB_PATH_EXISTS_IN = COLUMN_NAME + " == $values[*])";
    /**
     * jsonb_path_exists函数，not in操作
     */
    public static final String JSONB_PATH_EXISTS_NOT_IN = COLUMN_NAME + " != $values[*])";


    /**
     * 表单数据key,字符串形式
     */
    public static final String FORM_DATA_KEY_TEXT = "form_data->>'%s'";

}
