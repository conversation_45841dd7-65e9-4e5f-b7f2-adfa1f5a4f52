package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.nocode.app.dto.ZdAppCodeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 2025.0422
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "分组DTO")
public class ZdPageGroupSaveDTO extends ZdAppCodeDTO {
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称不能超过100个字符")
    @ApiModelProperty(value = "名称", example = "abc", required = true)
    private String name;

    @NotNull(message = "排序不能为空")
    @Min(value = -1, message = "排序不能小于-1")
    @ApiModelProperty(value = "排序", example = "0", required = true)
    private Integer sort;

    @NotNull(message = "父级主键不能为空")
    @Min(value = -1, message = "父级主键不能小于-1")
    @ApiModelProperty(value = "父节主键,没有时传递0", example = "0", required = true)
    private Long parentId;


}
