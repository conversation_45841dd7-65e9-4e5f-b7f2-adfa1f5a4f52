package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.form.enumerate.LogicOperatorEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0326
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "数据范围自定义过滤条件")
@Data
public class ZdFormDataScopeCustomConditionDTO {
    /**
     * 条件关系
     */
    @NotNull(message = "条件关系，不能为空")
    @ApiEnumProperty(required = true)
    private LogicOperatorEnum logicOperator;
    @ApiModelProperty("搜索字段")
    @NotEmpty(message = "搜索条件不能为空")
    private List<ZdFormDataSearchFieldDTO> conditions;
}
