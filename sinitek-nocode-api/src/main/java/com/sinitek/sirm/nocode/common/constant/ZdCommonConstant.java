package com.sinitek.sirm.nocode.common.constant;

import com.baomidou.mybatisplus.core.toolkit.Constants;

/**
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */
public class ZdCommonConstant {
    private ZdCommonConstant() {
    }

    /**
     * 名称
     */
    public static final String NAME = "零代码平台";

    /**
     * 枚举类名
     */
    public static final String ENUMERATE_CLASS = "com.sinitek.sirm.nocode.%s.enumerate.%s";
    /**
     * 表名
     */
    public static final String TABLE_NAME = "${" + Constants.WRAPPER_DOT + "tableName" + "}";

    /**
     * 应用的token名称
     */
    public static final String APPLICATION_TOKEN = "app_access_token";

    /**
     * 忽略的属性
     */
    public static final String IGNORE_PROPERTY = "entityNameValue";
}
