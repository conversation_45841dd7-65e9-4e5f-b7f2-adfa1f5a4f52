package com.sinitek.sirm.nocode.common.feign;

import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @version 2025.0519
 * @since 1.0.0-SNAPSHOT
 */
@FeignClient(
        name = "${sinicube.sirmapp.remote.service-name:CLOUD-SIRMAPP}",
        contextId = "remoteOrgService",
        url = "${sinicube.sirmapp.remote.url:}"
)
public interface IRemoteOrgUserService {

    @GetMapping({"/frontend/api/org/currentuser"})
    ResponseEntity<RequestResult<UserDTO>> current(@RequestHeader("accesstoken") String accesstoken);
}
