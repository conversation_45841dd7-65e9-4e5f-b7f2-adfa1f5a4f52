package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.OrderItemDTO;
import com.sinitek.sirm.nocode.form.enumerate.ValueTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 2025.0530
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "自定义排序对象")
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomOrderItemDTO extends OrderItemDTO {
    @NotNull(message = "值类型不能为空")
    @ApiEnumProperty(required = true)
    private ValueTypeEnum valueType;
}
