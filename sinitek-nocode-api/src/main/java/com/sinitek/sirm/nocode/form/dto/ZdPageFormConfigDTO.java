package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0409
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单配置DTO")
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdPageFormConfigDTO extends ZdIdDTO {

    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码", example = "a10000")
    private String formCode;

    /**
     * 数据表的名称,当为普通表单或者是流程表单时，这个字段有值
     */
    @ApiModelProperty(value = "数据表的名称,当为普通表单或者是流程表单时，这个字段有值", example = "zd_page_form_data_0")
    private String tableName;

    /**
     * 当表单为流程表单时，这个字段有值
     */
    @ApiModelProperty(value = "当表单为流程表单时，这个字段有值", example = "yearWorkFlow")
    private String processcode;
}
