package com.sinitek.sirm.nocode.common.constant;

/**
 * <AUTHOR>
 * @version 2025.0509
 * @since 1.0.0-SNAPSHOT
 * 缓存的key
 */

public class CacheKeyConstant {

    private CacheKeyConstant() {
    }
    /**
     * 角色缓存key 默认300秒
     */
    public static final String ROLE_CACHE_KEY = "nocode-role-cache";

    /**
     * 默认120s
     * 权限缓存
     */
    public static final String PAGE_AUTH_CACHE_KEY = "nocode-form-auth-cache";
    /**
     * 钉钉token缓存 默认2小时
     */
    public static final String DING_TALK_TOKEN = "nocode-dingtalk-cache";
    /**
     * 表单配置缓存，默认24小时
     */
    public static final String FORM_CONFIG = "nocode-form-config-cache";
}
