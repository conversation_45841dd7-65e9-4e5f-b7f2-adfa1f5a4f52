package com.sinitek.sirm.nocode.common.dto;

import com.sinitek.sirm.nocode.app.support.AppCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0328
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "编码DTO")
public class ZdCodeDTO implements AppCodeSupplier {
    @NotBlank(message = "编码不能为空")
    @ApiModelProperty(value = "编码", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
    @Length(max = 50, message = "编码不能超过50个字符")
    private String code;
}
