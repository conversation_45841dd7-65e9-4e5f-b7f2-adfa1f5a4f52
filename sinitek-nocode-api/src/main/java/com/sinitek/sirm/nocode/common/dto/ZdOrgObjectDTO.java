package com.sinitek.sirm.nocode.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 2025.0522
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "组织架构信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ZdOrgObjectDTO {
    @ApiModelProperty("组织结构对象id")
    private String orgId;
    @ApiModelProperty("组织结构的名称")
    private String orgName;
}
