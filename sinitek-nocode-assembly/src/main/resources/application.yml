server:
  port: 8097
  servlet:
    encoding:
      charset: utf-8
      enabled: true
      force: true
    context-path: /zhida
spring:
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

  application:
    name: SINITEK-NOCODE-BACKEND
  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: -1
      max-request-size: -1
# 属性类 com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 扫描该包下的 typehandler类，注册到Mybatis
  typeHandlersPackage: com.sinitek.data.mybatis.typehandlers
  global-config:
    # 是否关闭MP3.0自带的banner
    banner: true
    # 雪花算法使用的 worker-id、datacenter-id,集群环境下不同机器需要不同
    worker-id: 1
    datacenter-id: 1
    db-config:
      # keepGlobalFormat=true的字段会根据该值进行String.format,主要处理数据库关键字
      # '`%s`' 为mysql,  '%s' 为 oracle
      #column-format: '`%s`'
      column-format: '%s'
      #主键类型
      id-type: ASSIGN_ID
      #是否开启自定义的 IDGenerator策略
      id-customer-enable: false
      #是否开启兼容EntityName(使用Metadb_Entity表的记录进行兼容)
      compatible-entityname-enable: true
      #更新的时候是否判断设置为null,默认是跳过 null的更新的。现在设置为 null 也是可以更新。
      update-strategy: ignored
  type-enums-package: com.sinitek.sirm.nocode.*.enumerate

feign:
  hystrix:
    enabled: true

management:
  endpoints:
    shutdown:
      enabled: true
    web:
      exposure:
        include: "*"    #开放全部监控端点
  health:
    redis:
      enabled: false
    elasticsearch:
      enabled: false

## 是否开启子服务会话验证,先关闭
sirm:
  cloud:
    enable: true
sinicube:
  tempdir:
    auto-clean-enabled: false

  rsa:
    privateKey: 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMJvyftn+1AKE3KaXzGYpS4yuZyKHkO8liF3IpcINJFLWXI7yiIBLWWbFLbgwnV/aJV9iJmUQ6sjS2pw9fmxAvoDP+4uHJlAaUkzVHZOyW/mHaCdmREtEoq1PXo5ZJyKnK9Xs0B7wrUPjeyTUSrFZsyGm4gMJSEdPhriauuyzzPNAgMBAAECgYEAv84bSECZL6ng6WrTgU99kwdDBuNRW6zLxROLMcZMZRAZmpDUo5rZt6O2WXl7GwGmn+GIQUh7QHW+za/FVp2BunsT1qYgIXtRneocNNvhk668Y9vHzF1p0j6ThXFHf1aHQTZC7hTbKzqc7ju98JAx+EThB1XE8A9gLBNj4Dz9sEECQQDukED32WPKXhuMReMj99sRNijwOi/GZSaiSmaseUZ7NSOFUMiBysS4MCJeQFWUupdPLXtY6IQkfwWO40NcKipFAkEA0KXhlY47gYPH0OEzKHLxvB+hdc7tCb2OXOlJp13uC2MMhEX9EMuxHsfKOHBVaEopa8HahsqEQumIy5lBnkT/6QJAXF5QOWbbg3xikDJzGvcZxBVDTphI7Tk29zl1faxLROMzSKgZql93QZWpiPLB8B86/LYwfyEwIO1pmoSWDTZqPQJAAaLrbALbkJ55+LdrcUvFfZY56/Sdg1ALR8tEmp1v+oLiXD84RuNyFi229k5bPSAnxwKUNi5sbyHpwr8G9rjP0QJBAKjZBywSFSdcamDoLXkKXLwmv9pz/uLmPveAqVR+YnVhelUQMUcBqBy4zqRCAdYxbb0wjVLSvTDIa8lSefuzO8o='
    publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCb8n7Z/tQChNyml8xmKUuMrmcih5DvJYhdyKXCDSRS1lyO8oiAS1lmxS24MJ1f2iVfYiZlEOrI0tqcPX5sQL6Az/uLhyZQGlJM1R2Tslv5h2gnZkRLRKKtT16OWScipyvV7NAe8K1D43sk1EqxWbMhpuIDCUhHT4a4mrrss8zzQIDAQAB'
  # 属性 com.sinitek.sirm.i18n.properties.I18nProperties
  # 配置类 com.sinitek.sirm.config.SpringMessageConfig
  i18n:
    # 是否开启国际化
    enable: false
    # 后端加载的message
    #basename: classpath:message/messages-common
    # 默认的message编码
    defaultEncoding: UTF-8
    # code找不到对应的message时,是否默认使用code返回
    useCodeAsDefaultMessage: true
    # 默认的语言环境
    defaultLocale: zh_CN
    # 系统支持的语言环境
    supportedLocaleInfoList:
      - locale: zh_CN
        localeName: 简体中文
        localeChineseName: 简体中文
      - locale: zh_HK
        localeName: 繁體中文
        localeChineseName: 繁体中文
    # 语言环境切换时统一使用的headerName
    headerName: lang



hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 10000

ribbon:
  ConnectTimeout: 3000
  ReadTimeout: 6000

## 指定临时目录地址为本地
localsettingpriority: true
setting:
  tempdir: ${java.io.tmpdir}
llm:
  sinitek-chat:
    server: http://192.168.22.247
    api-key: app-8M8IsV3mgTWrWUsVHNz4Ln5T
  sinitek-chat-fast:
    server: http://192.168.22.247
    api-key: app-6Bu240R0fcmWhzhi4InWzrzs
  sinitek-image:
    server: http://192.168.22.247
    api-key: app-Uk8Q2CgwfZf1GNiU0ywYsQSU
  schema-gen-agent:
    server: http://192.168.22.247
    api-key: app-5FQH122Q4zp2DYZHA4KKMkyy
  manual-agent:
    server: http://192.168.22.247
    api-key: todo
  sql-gen-chat:
    server: http://192.168.22.247
    api-key: app-6NDWq00a4cPJPQiddYwig0Ak
  diagram-gen-chat:
    server: http://192.168.22.247
    api-key: app-D53L7blMHQbSRC2y4uB9evYO
