package com.sinitek.sirm.nocode.test.tool.util;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/4/22
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class LcNameStyleUtil {

    /**
     * 将传入字符串的第一个字符转换为大写。 如果第一个字符已经是大写，则直接返回原字符串。
     *
     * @param s 待转换的字符串。
     * @return 转换后的新字符串，如果原字符串第一个字符已经是大写，则返回原字符串。
     */
    public static String toUpperCaseFirstOne(String s) {
        return org.apache.commons.lang3.StringUtils.capitalize(s);
    }

    /**
     * 将驼峰命名转换为下划线命名。
     *
     * aDay -> a_day
     * ADay -> a_day
     *
     * @param para 需要转换的字符串。如果输入为null，则返回null。
     * @return 转换后的字符串。转换过程中，所有的大写字母都会被替换为"_"+小写字母， 并且最终结果会转换为小写字母格式。
     * 如果输入字符串中已经包含下划线， 则不会进行转换。
     */
    public static String camelToUnderline(String para) {
        return StringUtils.camelToUnderline(para);
    }

    /**
     * 下划线转大驼峰
     *
     * a_b -> AB
     * a_day -> ADay
     */
    public static String underlineToCamel(String param) {
        return underlineToCamel(param, true);
    }

    /**
     * 下划线转小驼峰
     *
     * a_b -> aB
     * a_day -> aDay
     */
    public static String underlineTosmallCamel(String param) {
        return underlineToCamel(param, false);
    }

    /**
     * 将下划线风格字符串转换为驼峰式字符串
     *
     * @param para 待转换的下划线风格字符串。
     * @param firstUpperCase 是否将首字母转换为大写。
     * @return 转换后的驼峰式字符串。
     */
    private static String underlineToCamel(String para, boolean firstUpperCase) {
        String sb = StringUtils.underlineToCamel(para);
        if (firstUpperCase) {
            return toUpperCaseFirstOne(sb);
        } else {
            return sb;
        }
    }
}
