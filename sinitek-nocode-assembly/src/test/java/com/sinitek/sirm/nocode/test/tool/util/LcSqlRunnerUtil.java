package com.sinitek.sirm.nocode.test.tool.util;

import com.baomidou.mybatisplus.core.assist.ISqlRunner;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionUtils;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 8/18/2023 5:04 PM
 */
public class LcSqlRunnerUtil implements ISqlRunner {

    /**
     * 这是mybatis源码,这里只能这么写
     */
    private final Log log = LogFactory.getLog(LcSqlRunnerUtil.class);
    // 单例Query
    public static final LcSqlRunnerUtil DEFAULT = new LcSqlRunnerUtil();
    // 默认FACTORY
    // public static SqlSessionFactory FACTORY;
    private SqlSessionFactory sqlSessionFactory;

    private Class<?> clazz;

    public LcSqlRunnerUtil(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public LcSqlRunnerUtil() {
        this.sqlSessionFactory = SqlHelper.FACTORY;
    }

    public LcSqlRunnerUtil(Class<?> clazz) {
        this.clazz = clazz;
    }

    /**
     * 获取默认的SqlQuery(适用于单库)
     *
     * @return ignore
     */
    public static LcSqlRunnerUtil db() {
        // 初始化的静态变量 还是有前后加载的问题 该判断只会执行一次
        if (DEFAULT.sqlSessionFactory == null) {
            DEFAULT.sqlSessionFactory = SqlHelper.FACTORY;
        }
        return DEFAULT;
    }

    /**
     * 根据当前class对象获取SqlQuery(适用于多库)
     *
     * @param clazz ignore
     * @return ignore
     */
    public static com.baomidou.mybatisplus.extension.toolkit.SqlRunner db(Class<?> clazz) {
        return new com.baomidou.mybatisplus.extension.toolkit.SqlRunner(clazz);
    }

    @Transactional
    @Override
    public boolean insert(String sql, Object... args) {
        SqlSession sqlSession = sqlSession();
        try {
            return SqlHelper.retBool(sqlSession.insert(ISqlRunner.INSERT, sqlMap(sql, args)));
        } finally {
            closeSqlSession(sqlSession);
        }
    }

    @Transactional
    @Override
    public boolean delete(String sql, Object... args) {
        SqlSession sqlSession = sqlSession();
        try {
            return SqlHelper.retBool(sqlSession.delete(ISqlRunner.DELETE, sqlMap(sql, args)));
        } finally {
            closeSqlSession(sqlSession);
        }
    }

    /**
     * 获取sqlMap参数
     *
     * @param sql 指定参数的格式: {0}, {1}
     * @param args 仅支持String
     * @return ignore
     */
    private Map<String, String> sqlMap(String sql, Object... args) {
        Map<String, String> sqlMap = CollectionUtils.newHashMapWithExpectedSize(1);
        sqlMap.put(ISqlRunner.SQL, StringUtils.sqlArgsFill(sql, args));
        return sqlMap;
    }

    /**
     * 获取sqlMap参数
     *
     * @param sql 指定参数的格式: {0}, {1}
     * @param page 分页模型
     * @param args 仅支持String
     * @return ignore
     */
    @SuppressWarnings({"squid:S109"})
    private Map<String, Object> sqlMap(String sql, IPage<Object> page, Object... args) {
        Map<String, Object> sqlMap = CollectionUtils.newHashMapWithExpectedSize(2);
        sqlMap.put(ISqlRunner.PAGE, page);
        sqlMap.put(ISqlRunner.SQL, StringUtils.sqlArgsFill(sql, args));
        return sqlMap;
    }

    @Transactional
    @Override
    public boolean update(String sql, Object... args) {
        SqlSession sqlSession = sqlSession();
        try {
            return SqlHelper.retBool(sqlSession.update(ISqlRunner.UPDATE, sqlMap(sql, args)));
        } finally {
            closeSqlSession(sqlSession);
        }
    }

    /**
     * 根据sql查询Map结果集
     * <p>SqlRunner.db().selectList("select * from tbl_user where name={0}", "Caratacus")</p>
     *
     * @param sql sql语句，可添加参数，格式：{0},{1}
     * @param args 只接受String格式
     * @return ignore
     */
    @Override
    public List<Map<String, Object>> selectList(String sql, Object... args) {
        SqlSession sqlSession = sqlSession();
        try {
            return sqlSession.selectList(ISqlRunner.SELECT_LIST, sqlMap(sql, args));
        } finally {
            closeSqlSession(sqlSession);
        }
    }

    /**
     * 根据sql查询一个字段值的结果集
     * <p>注意：该方法只会返回一个字段的值， 如果需要多字段，请参考{@code selectList()}</p>
     *
     * @param sql sql语句，可添加参数，格式：{0},{1}
     * @param args 只接受String格式
     * @return ignore
     */
    @Override
    public List<Object> selectObjs(String sql, Object... args) {
        SqlSession sqlSession = sqlSession();
        try {
            return sqlSession.selectList(ISqlRunner.SELECT_OBJS, sqlMap(sql, args));
        } finally {
            closeSqlSession(sqlSession);
        }
    }

    /**
     * 根据sql查询一个字段值的一条结果
     * <p>注意：该方法只会返回一个字段的值， 如果需要多字段，请参考{@code selectOne()}</p>
     *
     * @param sql sql语句，可添加参数，格式：{0},{1}
     * @param args 只接受String格式
     * @return ignore
     */
    @Override
    public Object selectObj(String sql, Object... args) {
        return SqlHelper.getObject(log, selectObjs(sql, args));
    }

    @Override
    public int selectCount(String sql, Object... args) {
        SqlSession sqlSession = sqlSession();
        try {
            return SqlHelper.retCount(sqlSession.<Integer>selectOne(
                ISqlRunner.COUNT, sqlMap(sql, args)));
        } finally {
            closeSqlSession(sqlSession);
        }
    }

    @Override
    public Map<String, Object> selectOne(String sql, Object... args) {
        return SqlHelper.getObject(log, selectList(sql, args));
    }

    @Override
    public <E extends IPage<Map<String, Object>>> E selectPage(E page, String sql, Object... args) {
        if (null == page) {
            return null;
        }
        page.setRecords(sqlSession().selectList(ISqlRunner.SELECT_LIST, sqlMap(sql, page, args)));
        return page;
    }

    /**
     * 获取Session 默认自动提交
     */
    private SqlSession sqlSession() {
        return (clazz != null) ? SqlSessionUtils.getSqlSession(
            GlobalConfigUtils.currentSessionFactory(clazz))
            : SqlSessionUtils.getSqlSession(sqlSessionFactory);
    }

    /**
     * 释放sqlSession
     *
     * @param sqlSession session
     */
    @SuppressWarnings({"squid:S1117"})
    private void closeSqlSession(SqlSession sqlSession) {
        SqlSessionFactory sqlSessionFactory;
        if (clazz != null) {
            sqlSessionFactory = GlobalConfigUtils.currentSessionFactory(clazz);
        } else {
            sqlSessionFactory = DEFAULT.sqlSessionFactory;
        }
        SqlSessionUtils.closeSqlSession(sqlSession, sqlSessionFactory);
    }
}
