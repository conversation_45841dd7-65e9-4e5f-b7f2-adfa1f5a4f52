package com.sinitek.sirm.nocode.test.tool;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.aMapWithSize;
import static org.hamcrest.Matchers.containsInRelativeOrder;
import static org.hamcrest.Matchers.emptyIterable;
import static org.hamcrest.Matchers.emptyOrNullString;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasItems;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.iterableWithSize;
import static org.hamcrest.Matchers.not;

import cn.hutool.core.lang.func.Consumer3;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.SirmApplication;
import com.sinitek.sirm.nocode.test.tool.support.LcDoAny;
import com.sinitek.sirm.nocode.test.tool.util.LcCheckUtil;
import com.sinitek.sirm.nocode.test.tool.util.LcExceptionTestUtil;
import com.sinitek.sirm.nocode.test.tool.util.LcLambdaUtil;
import com.sinitek.sirm.nocode.test.tool.util.LcSqlRunnerUtil;
import com.sinitek.sirm.nocode.test.tool.util.LcTestDataUtil;
import com.sinitek.sirm.nocode.test.tool.util.LcTestFileUtil;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.hamcrest.Matchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
@Slf4j
@ActiveProfiles("unit-test")
@SpringBootTest(classes = SirmApplication.class)
@AutoConfigureMockMvc
public class LcBeanUnitTest {

    @Autowired
    protected SqlSessionFactory sqlSessionFactory;

    @Autowired
    protected Environment environment;

    protected LcSqlRunnerUtil sqlRunner;

    protected void initSqlRrunner() {
        this.sqlRunner = new LcSqlRunnerUtil(this.sqlSessionFactory);
    }

    protected DataSource getDataSource() {
        return this.sqlSessionFactory.getConfiguration().getEnvironment().getDataSource();
    }

    protected void runSqlScript(String resource) throws SQLException, IOException {
        LcTestDataUtil.runScript(
            this.getDataSource(),
            resource);
    }

    @SuppressWarnings({"squid:ReturnMapCheck", "squid:S109", "squid:CatchCheck"})
    protected <T> void shouldHasException(VoidFunction func, Class<?> type) {
        try {
            func.exec();
        } catch (Exception e) {
            assertThat(e, Matchers.instanceOf(type));
        }
    }

    @SuppressWarnings({"squid:ReturnMapCheck", "squid:S109", "squid:CatchCheck"})
    protected <T> void shouldHasException(VoidFunction func, String expectedCode,
        String expectedMessage) {
        try {
            func.exec();
        } catch (Exception e) {
            assertThat(e, Matchers.instanceOf(BussinessException.class));
            BussinessException bussinessException = (BussinessException) e;
            String code = bussinessException.getCode();
            assertThat(code, Matchers.is(expectedCode));
            String message = bussinessException.getMessage();
            assertThat(message, Matchers.is(expectedMessage));
        }
    }

    protected <T> void checkPropertyValue(T data, Map<String, Object> expectData,
        SFunction<T, Object> valueGetter) {
        LcCheckUtil.checkPropertyValue(data, expectData, valueGetter);
    }

    protected <T> void checkDBSnapshotPropertyValue(T data, Map<String, Object> expectData,
        SFunction<T, Object> valueGetter) {
        LcCheckUtil.checkDBSnapshotPropertyValue(data, expectData, valueGetter);
    }

    protected <T> void checkDBSnapshotPropertyValue(T data, Map<String, Object> expectData,
        SFunction<T, Object> valueGetter,
        Map<String, Consumer3<T, Map<String, Object>, Object>> customCheck) {
        LcCheckUtil.checkDBSnapshotPropertyValue(data, expectData, valueGetter, customCheck);
    }

    public <T> Map<String, Consumer3<T, Map<String, Object>, Object>> toStringKeyMap(
        Map<SFunction<T, Object>, Consumer3<T, Map<String, Object>, Object>> map) {
        return map.entrySet().stream().collect(
            Collectors.toMap(entry -> LcLambdaUtil.getFieldNameByLambda(entry.getKey()),
                Entry::getValue));
    }

    public String getFieldNameByLambda(SFunction<?, Object> lambda) {
        return LcLambdaUtil.getFieldNameByLambda(lambda);
    }

    protected void shouldBeEquals(Object actValue, Object expectedValue) {
        LcCheckUtil.shouldBeEquals(actValue, expectedValue);
    }

    protected void shouldBeNull(Object actValue) {
        assertThat(actValue, Matchers.nullValue());
    }

    protected void shouldNotNull(Object actValue) {
        assertThat(actValue, Matchers.notNullValue());
    }

    protected <T> void shouldNotEmpty(Collection<T> value) {
        assertThat(value, hasSize(greaterThan(0)));
    }

    protected void shouldNotBlank(String value) {
        assertThat(value, not(emptyOrNullString()));
    }

    protected void shouldDateBeEquals(Date actValue, String dateStr, String parttern) {
        Date date = null;
        try {
            date = DateUtils.parseDate(dateStr, parttern);
        } catch (Exception e) {
            log.error("日期字符串 {} 根据 pattern [{}] 格式化失败: {} ", dateStr, parttern,
                e.getMessage(), e);
            throw new BussinessException(e.getMessage());
        }

        assertThat(actValue, Matchers.equalTo(date));
    }

    protected void shouldBeInstanceType(Object actValue, Class<?> type) {
        assertThat(actValue, Matchers.instanceOf(type));
    }

    protected <E> void shouldBeEmpty(Collection<E> collection) {
        assertThat(collection, emptyIterable());
    }

    protected <E> void shouldHasSize(Collection<E> collection, int size) {
        assertThat(collection, iterableWithSize(size));
    }

    protected <E, T> void shouldHasSize(Map<E, T> collection, int size) {
        assertThat(collection, aMapWithSize(size));
    }

    protected <T> void shouldHasItems(Collection<T> collection, T... items) {
        assertThat(collection, hasItems(items));

    }

    protected <T> void shouldContainsInRelativeOrder(Collection<T> collection, T... items) {
        assertThat(collection, containsInRelativeOrder(items));
    }

    protected void shouldNoExcetion(LcDoAny doAny) {
        LcExceptionTestUtil.shouldNoExcetion(doAny);
    }

    protected void shouldHasBussinessExcetion(LcDoAny doAny, String code, String message) {
        LcExceptionTestUtil.shouldHasBussinessExcetion(doAny, code, message);
    }

    protected String readJsonFile(String filePath) {
        return LcTestFileUtil.readTxtFile(filePath);
    }

    protected File readFile(String filePath) {
        return LcTestFileUtil.readFile(filePath);
    }

    protected interface VoidFunction {

        void exec();
    }

    protected void clearCache() {
        // 无需实现
    }

    protected <T> void checkSDKSuccessResponse(RequestResult<T> result) {
        this.shouldNotNull(result);
        String resultcode = result.getResultcode();
        this.shouldBeEquals(resultcode, RequestResult.SUCCESS_CODE);
    }
}
