<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sinitek.sirm</groupId>
        <artifactId>sinitek-nocode</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>sinitek-nocode-service-design</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <skip.jar>false</skip.jar>
    </properties>


    <dependencies>
        <!-- 人大金仓数据库驱动 -->
        <!-- <dependency>
             <groupId>cn.com.kingbase</groupId>
             <artifactId>kingbase8</artifactId>
             <version>9.0.0</version>
         </dependency>-->

        <dependency>
            <groupId>com.sinitek.sirm</groupId>
            <artifactId>sinitek-nocode-dal</artifactId>
        </dependency>


        <!-- 达梦数据库依赖 -->
        <!--        <dependency>-->
        <!--            <groupId>com.dm8</groupId>-->
        <!--            <artifactId>DmJdbcDriver</artifactId>-->
        <!--            <version>1.0.0</version>-->
        <!--        </dependency>-->

        <!-- 南大通用数据库依赖 -->
        <!--        <dependency>-->
        <!--            <groupId>com.gbase</groupId>-->
        <!--            <artifactId>ifxjdbc</artifactId>-->
        <!--            <version>1.0.0</version>-->
        <!--        </dependency>-->

        <!-- 业务日志切到es进行存储 -->
        <!--        <dependency>-->
        <!--            <groupId>com.sinitek.sinicube</groupId>-->
        <!--            <artifactId>sinitek-sinicube-business-log-by-es</artifactId>-->
        <!--        </dependency>-->

        <!-- 附件存储支持切到minio -->
        <!--        <dependency>-->
        <!--            <groupId>com.sinitek.sinicube</groupId>-->
        <!--            <artifactId>sinitek-sinicube-attachment-by-minio</artifactId>-->
        <!--            <version>${sinicube.version}</version>-->
        <!--        </dependency>-->

        <!-- 消息模版支持企业微信 -->
        <!-- <dependency>
             <groupId>com.sinitek.sinicube</groupId>
             <artifactId>sinitek-sinicube-wxwork</artifactId>
         </dependency>-->


        <!--  <dependency>
              <groupId>io.springfox</groupId>
              <artifactId>springfox-swagger-ui</artifactId>
          </dependency>-->


        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek_commoncore</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-cloud-base</artifactId>
        </dependency>


        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-cloud-sirmapp-sdk</artifactId>
        </dependency>


        <!-- 注册中心默认选择 Nacos, 如果使用eureka可自行切换-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

    </dependencies>
</project>
