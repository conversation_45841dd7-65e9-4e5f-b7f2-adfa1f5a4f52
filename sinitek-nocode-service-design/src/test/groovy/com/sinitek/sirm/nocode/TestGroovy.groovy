package com.sinitek.sirm.nocode

import com.sinitek.sirm.nocode.app.service.IZdAppManagerService
import com.sinitek.sirm.nocode.app.service.IZdAppService
import com.sinitek.sirm.nocode.form.service.IZdPageFormConfigService
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService
import com.sinitek.sirm.nocode.form.service.IZdPageFormService
import com.sinitek.sirm.nocode.page.service.IZdPageService
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Unroll

import javax.annotation.Resource

/**
 * <AUTHOR>
 * @version 2025.0327
 * @since 1.0.0-SNAPSHOT
 */
@ContextConfiguration(classes = SirmApplication)
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("dev")
class TestGroovy extends Specification {

    @Resource
    private IZdAppService appService;
    @Resource
    private IZdAppManagerService appManagerService;
    @Resource
    private IZdPageService pageService;
    @Resource
    private IZdPageFormService pageFormService;
    @Resource
    private IZdPageFormDataService pageFormDataService;

    @Resource
    private IZdPageFormConfigService pageFormConfigService;

    @Unroll
    def "发布普通问卷,入参：#request,接口返回状态码:#code"() {
        /*def app = new ZdApp()
        app.setId(1);
        app.setCode("a0");
        app.setName("测试应用");
        app.setDescription("测试应用");
        app.setAppSecret("xxxabc");
        app.setStatus(StatusEnum.ENABLE);
        app.setIconfont("apple");
        appService.saveOrUpdate(app);*/

    }
}
