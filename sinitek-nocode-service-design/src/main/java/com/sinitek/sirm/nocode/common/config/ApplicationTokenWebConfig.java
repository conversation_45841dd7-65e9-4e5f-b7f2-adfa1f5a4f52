package com.sinitek.sirm.nocode.common.config;

import com.sinitek.sirm.nocode.common.interceptor.ApplicationTokenInterceptor;
import com.sinitek.sirm.nocode.common.properties.NocodeSecurityProperties;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2025.0507
 * @since 1.0.0-SNAPSHOT
 */
public class ApplicationTokenWebConfig implements WebMvcConfigurer {
    @Autowired
    @Lazy
    private ApplicationTokenInterceptor applicationTokenInterceptor;

    @Resource
    private NocodeSecurityProperties sirmSecurityProperties;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 最先。
        InterceptorRegistration registration = registry.addInterceptor(applicationTokenInterceptor);
        registration.addPathPatterns(StringUtils.split(sirmSecurityProperties.getPath(), ","));
        String excludePath = sirmSecurityProperties.getExcludepath();
        if (StringUtils.isNotBlank(excludePath)) {
            registration.excludePathPatterns(StringUtils.split(excludePath, ","));
        }
    }

}
