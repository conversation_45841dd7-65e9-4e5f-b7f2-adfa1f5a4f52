package com.sinitek.sirm.nocode.appmanager.aspect;

import com.sinitek.sirm.nocode.app.support.AppCodeSupplier;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 2025.0415
 * @description 应用权限注解
 * <h5>如何获取参数里面的应用编码，有以下几种方法</h2>
 * <ul>
 *     <li> 1. 直接使用value值，这个支持Spring EL表达式，比如：@ZdAppRight("#p0.appCode")
 *     </li>
 *     <li> 2. 参数实现接口{@link AppCodeSupplier}</li>
 *     <li> 3. 使用 RequestParam 注解，参数名称为 appCode</li>
 *     <li> 4. 有且只有一个字符串参数，会被认为是appCode</li>
 * </ul>
 * @since 1.0.0-SNAPSHOT
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ZdAppRight {
    /**
     * 应用编码
     *
     * @return 获取应用编的参数key, 支持 Spring EL 表达式
     */
    String value() default "";

    /**
     * 错误提示信息
     *
     * @return 错误提示信息
     */
    String messageCode() default "";

    /**
     * 是否检查应用状态
     *
     * @return 是否检查应用状态
     */
    boolean checkStatus() default true;

}
