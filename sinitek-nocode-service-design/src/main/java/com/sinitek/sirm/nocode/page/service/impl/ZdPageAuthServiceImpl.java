package com.sinitek.sirm.nocode.page.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.constant.CacheKeyConstant;
import com.sinitek.sirm.nocode.common.dto.ZdOrgObjectDTO;
import com.sinitek.sirm.nocode.common.utils.BeanUtilsEx;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.common.utils.CopyUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormButtonsSupportDTO;
import com.sinitek.sirm.nocode.page.dao.ZdPageAuthDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthSaveDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthSearchParamDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageAuth;
import com.sinitek.sirm.nocode.page.enumerate.DataScopeEnum;
import com.sinitek.sirm.nocode.page.enumerate.FieldAuthFlagEnum;
import com.sinitek.sirm.nocode.page.enumerate.MemberTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.event.PageCreateEvent;
import com.sinitek.sirm.nocode.page.mapper.ZdPageAuthMapper;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.support.member.base.MemberTest;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.org.dto.EmployeeSearchDTO;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.enumerate.OrgType;
import com.sinitek.sirm.org.service.IOrgService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025-03-12 13:38:21
 * @description 针对表【zd_page_auth(页面权限表)】的数据库操作Service实现
 */
@Service
public class ZdPageAuthServiceImpl extends BaseDAO<ZdPageAuthMapper, ZdPageAuth, ZdPageAuthDAO>
        implements IZdPageAuthService, ITableResultFormat<ZdPageAuthDTO> {

    @Resource
    private Map<String, MemberTest> memberTestMap;

    @Resource
    private ZdPageAuthServiceImpl self;

    @Resource
    private IOrgService orgService;

    @Cacheable(value = CacheKeyConstant.PAGE_AUTH_CACHE_KEY, key = "#pageCode+#authType+#orgId")
    @Override
    public ZdPageAuthDTO rightQuery(String pageCode, PageAuthTypeEnum authType, String orgId) {
        if (StringUtils.isBlank(orgId)) {
            // 当前登录人不能为空
            throw new BussinessException("3000006");
        }
        List<ZdPageAuthDTO> list = list(pageCode, authType);
        for (ZdPageAuthDTO zdPageAuth : list) {
            if (test(zdPageAuth, orgId, new HashSet<>(), null)) {
                return zdPageAuth;
            }
        }
        return null;
    }

    @Override
    public ZdFormButtonsSupportDTO getFormButtonsSupport(String pageCode, String orgId) {
        ZdPageAuthDTO submitAuth = self.rightQuery(pageCode, PageAuthTypeEnum.SUBMIT_AUTH, orgId);
        ZdPageAuthDTO dataAuth = self.rightQuery(pageCode, PageAuthTypeEnum.DATA_AUTH, orgId);
        List<String> operationAuthList = new ArrayList<>();
        if (Objects.nonNull(submitAuth)) {
            // 操作权限
            operationAuthList.addAll(submitAuth.getOperationAuthList());
        }
        if (Objects.nonNull(dataAuth)) {
            // 操作权限
            operationAuthList.addAll(dataAuth.getOperationAuthList());
        }

        List<OperationAuthEnum> operationAuthEnums = OperationAuthEnum.fromListValue(operationAuthList);
        return new ZdFormButtonsSupportDTO(operationAuthEnums);
    }

    @Override
    public List<ZdPageAuthDTO> list(String pageCode, PageAuthTypeEnum authType) {
        LambdaQueryWrapper<ZdPageAuth> queryWrapper = query(pageCode, authType);
        return ConvertUtil.list(dao.list(queryWrapper));
    }

    @Override
    public Set<String> findAuthOrgIds(List<ZdPageAuthDTO> list) {
        Set<String> findOrgIdSet = new HashSet<>();
        if (CollectionUtils.isEmpty(list)) {
            return findOrgIdSet;
        }
        String orgId = CurrentUserFactory.getOrgId();
        list.forEach(a -> test(a, orgId, findOrgIdSet, null));
        return findOrgIdSet;
    }

    @Override
    public List<Employee> findAuthEmp(List<ZdPageAuthDTO> list) {
        List<Employee> employeeList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return employeeList;
        }
        String orgId = CurrentUserFactory.getOrgId();
        Set<String> orgIdSet = new HashSet<>();
        list.forEach(a -> test(a, orgId, orgIdSet, employeeList));
        Set<String> set = employeeList.stream().map(Employee::getId).collect(Collectors.toSet());
        List<String> orgIdList = orgIdSet.stream().filter(a -> !set.contains(a)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            EmployeeSearchDTO employeeSearchDTO = new EmployeeSearchDTO();
            employeeSearchDTO.setInservice("1");
            employeeSearchDTO.setOrgType(OrgType.EMPLOYEE.getEnumItemValue());
            employeeSearchDTO.setSelectEmpIdList(orgIdList);
            List<Employee> allEmployees = orgService.findAllEmployees(employeeSearchDTO);
            employeeList.addAll(allEmployees);
        }
        return employeeList.stream().filter(BeanUtilsEx.distinctByKey(Employee::getId)).collect(Collectors.toList());
    }

    @Override
    public List<Employee> findSubmitEmp(String pageCode) {
        List<ZdPageAuthDTO> list = list(pageCode, PageAuthTypeEnum.SUBMIT_AUTH);
        if (CollectionUtils.isNotEmpty(list)) {
            return findAuthEmp(list);
        }
        return Collections.emptyList();
    }

    /**
     * 测试权限
     *
     * @param zdPageAuthDTO 权限
     * @param currentOrgId  当前用户orgId
     * @param findOrgIdSet  获取到的orgId集合
     * @return 是否有权限
     */
    private boolean test(ZdPageAuthDTO zdPageAuthDTO, String currentOrgId, Set<String> findOrgIdSet, List<Employee> employeeList) {
        MemberTypeEnum memberType = zdPageAuthDTO.getMemberType();
        MemberTest memberTest = memberTestMap.get(memberType.getMemberBeanName());
        List<String> memberOrgIds = zdPageAuthDTO.getMemberOrgIdList();
        String[] orgIds = null;
        if (CollectionUtils.isNotEmpty(memberOrgIds)) {
            orgIds = memberOrgIds.toArray(new String[0]);
        }
        return memberTest.test(currentOrgId, orgIds, findOrgIdSet, employeeList);
    }

    private LambdaQueryWrapper<ZdPageAuth> query(String pageCode, PageAuthTypeEnum authType) {
        // 大排序为成员类型，小排序为权限排序
        return eqOrIn(ZdPageAuth::getPageCode, pageCode)
                .eq(Objects.nonNull(authType), ZdPageAuth::getAuthType, authType)
                .orderByDesc(ZdPageAuth::getMemberType)
                .orderByAsc(ZdPageAuth::getSort);
    }


    @Override
    public TableResult<ZdPageAuthDTO> search(ZdPageAuthSearchParamDTO param) {
        String pageCode = param.getPageCode();
        PageAuthTypeEnum authType = param.getAuthType();
        LambdaQueryWrapper<ZdPageAuth> query = eqOrIn(ZdPageAuth::getPageCode, pageCode)
                .eq(ZdPageAuth::getAuthType, authType)
                .orderByAsc(ZdPageAuth::getSort)
                .orderByDesc(ZdPageAuth::getId);
        String name = param.getName();
        if (StringUtils.isNotBlank(name)) {
            query.like(ZdPageAuth::getName, name);
        }
        Page<ZdPageAuth> page = param.buildPage();
        dao.page(page, query);
        return param.build(ConvertUtil.page(page), this);
    }


    @CacheEvict(value = CacheKeyConstant.PAGE_AUTH_CACHE_KEY, allEntries = true)
    @Override
    public Long copyPageAuth(Long id) {
        ZdPageAuth pageAuth = dao.getById(id);
        pageAuth.setId(null);
        dao.save(pageAuth);
        return pageAuth.getId();
    }


    @CacheEvict(value = CacheKeyConstant.PAGE_AUTH_CACHE_KEY, allEntries = true)
    @Override
    public Long saveOrUpdate(ZdPageAuthDTO pageAuthDTO) {
        //check(pageAuthDTO);
        ZdPageAuth zdPageAuth = idCheck(pageAuthDTO.getId());
        if (Objects.isNull(zdPageAuth)) {
            zdPageAuth = new ZdPageAuth();
        }
        CopyUtil.copyEntityProperties(pageAuthDTO, zdPageAuth);
        if (Objects.equals(zdPageAuth.getFieldAuth(), FieldAuthFlagEnum.INHERIT)) {
            zdPageAuth.setFieldAuthData(null);
        }
        dao.saveOrUpdate(zdPageAuth);
        return zdPageAuth.getId();
    }

    @Override
    public String getAppCodeByPageAuthId(Long id) {
        return getBaseMapper().getAppCodeByPageAuthId(id);
    }


    @CacheEvict(value = CacheKeyConstant.PAGE_AUTH_CACHE_KEY, allEntries = true)
    @Override
    public boolean removeById(Long id) {
        return dao.removeById(id);
    }


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> pageCodeList = appDeleteEvent.getCodeList();
        LambdaQueryWrapper<ZdPageAuth> queryWrapper = eqOrIn(ZdPageAuth::getPageCode, pageCodeList);
        // 删除页面权限
        dao.remove(queryWrapper);
    }


    /**
     * 创建页面权限
     *
     * @param pageCreateEvent 页面创建事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(condition = "T(com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum).isForm(#pageCreateEvent.source.pageType)")
    public void createPageAuth(PageCreateEvent pageCreateEvent) {
        ZdPageDTO source = pageCreateEvent.getSource();
        String code = source.getCode();
        // 新增一个提交权限
        ZdPageAuth zdPageAuthSubmit = new ZdPageAuth();
        zdPageAuthSubmit.setAuthType(PageAuthTypeEnum.SUBMIT_AUTH);
        zdPageAuthSubmit.setName("全部成员可提交数据");
        zdPageAuthSubmit.setDescription("可以发起当前流程的权限");
        zdPageAuthSubmit.setPageCode(code);
        zdPageAuthSubmit.setMemberType(MemberTypeEnum.ALL);
        zdPageAuthSubmit.setSort(0);
        zdPageAuthSubmit.setOperationAuthList(Collections.singletonList(OperationAuthEnum.SUBMIT.getValue()));
        // 保存提交权限
        dao.save(zdPageAuthSubmit);

        // 新增一个数据权限
        ZdPageAuth zdPageAuthData = new ZdPageAuth();
        zdPageAuthData.setAuthType(PageAuthTypeEnum.DATA_AUTH);
        zdPageAuthData.setName("全部成员可查看本人提交数据");
        zdPageAuthData.setDescription("对当前表单数据进行查看的权限");
        zdPageAuthData.setPageCode(code);
        // 针对所有成员
        zdPageAuthData.setMemberType(MemberTypeEnum.ALL);
        zdPageAuthData.setSort(0);
        // 本人提交的数据
        zdPageAuthData.setDataScopeList(Collections.singletonList(DataScopeEnum.SELF.getValue()));
        // 本人提交的数据可以拥有所有权限
        zdPageAuthData.setOperationAuthList(OperationAuthEnum.allDataValue());
        // 保存数据权限
        dao.save(zdPageAuthData);
    }

    private static void check(ZdPageAuthSaveDTO zdPageAuthDTO) {
        MemberTypeEnum memberType = zdPageAuthDTO.getMemberType();
        List<String> memberOrgIdList = zdPageAuthDTO.getMemberOrgIdList();
        if (!Objects.equals(MemberTypeEnum.ALL, memberType) && CollectionUtils.isEmpty(memberOrgIdList)) {
            throw new BussinessException("3000014");
        }
    }

    /**
     * 格式化
     *
     * @param data 数据
     * @return 格式化后的数据
     */
    @Override
    public List<ZdPageAuthDTO> format(List<ZdPageAuthDTO> data) {
        if (CollectionUtils.isNotEmpty(data)) {
            Set<String> orgIdSet = new HashSet<>();
            data.forEach(item -> {
                List<String> memberOrgIdList = item.getMemberOrgIdList();
                if (CollectionUtils.isNotEmpty(memberOrgIdList)) {
                    orgIdSet.addAll(memberOrgIdList);
                    List<ZdOrgObjectDTO> memberList = memberOrgIdList.stream().map(orgId -> new ZdOrgObjectDTO(orgId, null)).collect(Collectors.toList());
                    item.setMemberList(memberList);
                }
            });
            // 获取名称
            Map<String, String> orgNameMapByOrgIdList = orgService.getOrgNameMapByOrgIdList(new ArrayList<>(orgIdSet));
            data.forEach(item -> {
                List<ZdOrgObjectDTO> memberList = item.getMemberList();
                if (CollectionUtils.isNotEmpty(memberList)) {
                    memberList.forEach(member -> member.setOrgName(orgNameMapByOrgIdList.get(member.getOrgId())));
                }
            });
        }
        return data;
    }
}




