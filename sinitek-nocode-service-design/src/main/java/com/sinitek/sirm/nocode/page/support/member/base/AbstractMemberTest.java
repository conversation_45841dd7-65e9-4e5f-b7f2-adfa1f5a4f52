package com.sinitek.sirm.nocode.page.support.member.base;

import com.sinitek.sirm.org.dto.EmployeeSearchDTO;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0325
 * @since 1.0.0-SNAPSHOT
 */
public abstract class AbstractMemberTest implements MemberTest {

    @Resource
    private IOrgService orgService;


    @Override
    public boolean test(String currentOrgId, String[] orgIds, Set<String> findOrgIdSet, List<Employee> employeeList) {
        EmployeeSearchDTO employeeSearchDTO = new EmployeeSearchDTO();
        // 设置在职
        employeeSearchDTO.setInservice("1");
        // 参数设置
        setParam(employeeSearchDTO, orgIds);
        // 搜索符合条件的人员
        List<Employee> allEmployees = orgService.findAllEmployees(employeeSearchDTO);
        if (Objects.nonNull(employeeList)) {
            employeeList.addAll(allEmployees);
        }
        findOrgIdSet.addAll(allEmployees.stream().map(Employee::getId).collect(Collectors.toList()));
        // 只要匹配到一个就返回true
        return findOrgIdSet.contains(currentOrgId);
    }

    /**
     * 设置参数
     *
     * @param employeeSearchDTO 查询参数
     * @param orgIds            orgIds
     */
    public abstract void setParam(EmployeeSearchDTO employeeSearchDTO, String[] orgIds);


}
