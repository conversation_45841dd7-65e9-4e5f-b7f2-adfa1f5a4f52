package com.sinitek.sirm.nocode.common.properties;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 2025.0508
 * @since 1.0.0-SNAPSHOT
 */
@ConfigurationProperties(
        prefix = "nocode.security"
)
@Data
public class NocodeSecurityProperties {
    @ApiModelProperty(value = "需要鉴权的路径", example = "/**", required = true)
    private String path;
    @ApiModelProperty(value = "不需要鉴权的路径", required = true)
    private String excludepath;
}
