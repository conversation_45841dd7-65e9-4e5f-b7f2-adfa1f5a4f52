package com.sinitek.sirm.nocode.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.wall.WallConfig;
import com.alibaba.druid.wall.WallFilter;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2025.0619
 * @description PostgreSQL Druid WallFilter配置，移除PostgreSQL JSON函数限制
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class PgWallConfig implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(PgWallConfig.class);

    @Resource
    private DruidDataSource dataSource;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        dataSource.getProxyFilters().forEach(filter -> {
            if (filter instanceof WallFilter) {
                WallFilter wallFilter = (WallFilter) filter;
                WallConfig config = wallFilter.getConfig();

                // 移除PostgreSQL JSON函数限制
                removePostgreSQLFunctionRestrictions(config);

                log.info("PostgreSQL Druid WallFilter配置完成");
            }
        });
    }

    /**
     * 移除PostgreSQL函数限制
     *
     * @param config WallConfig配置对象
     */
    private void removePostgreSQLFunctionRestrictions(WallConfig config) {
        // 移除原有的current_schema限制
        config.getDenyFunctions().remove("current_schema");

        // 移除PostgreSQL JSON函数限制
        String[] jsonFunctions = {
            "jsonb_array_elements", "jsonb_array_elements_text",
            "jsonb_path_exists", "jsonb_extract_path", "jsonb_extract_path_text",
            "json_array_elements", "json_array_elements_text",
            "json_extract_path", "json_extract_path_text"
        };

        int removedCount = 0;
        for (String function : jsonFunctions) {
            if (config.getDenyFunctions().remove(function)) {
                removedCount++;
                log.debug("已移除函数限制: {}", function);
            }
        }

        log.info("已移除{}个PostgreSQL JSON函数限制", removedCount);
    }
}
