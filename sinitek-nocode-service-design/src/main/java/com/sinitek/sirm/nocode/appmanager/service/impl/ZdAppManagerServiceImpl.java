package com.sinitek.sirm.nocode.appmanager.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dao.ZdAppManagerDAO;
import com.sinitek.sirm.nocode.app.dto.ZdAppManagerDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppManagerSaveDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSettingDTO;
import com.sinitek.sirm.nocode.app.entity.ZdApp;
import com.sinitek.sirm.nocode.app.entity.ZdAppManager;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.app.mapper.ZdAppManagerMapper;
import com.sinitek.sirm.nocode.app.service.IZdAppManagerService;
import com.sinitek.sirm.nocode.app.support.IZdAppSettingCustomizer;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025-03-12 09:07:47
 * @description 针对表【zd_app_manager(应用管理员)】的数据库操作Service实现
 */
@Slf4j
@Service
public class ZdAppManagerServiceImpl extends BaseDAO<ZdAppManagerMapper, ZdAppManager, ZdAppManagerDAO>
        implements IZdAppManagerService, IZdAppSettingCustomizer {

    /**
     * 平台管理员的roleId
     */
    @Value("${sinicube.sirmapp.platform.roleid:0}")
    private String platformRoleId;

    @Resource
    private ZdOrgUtil orgUtil;


    @Override
    public boolean hasAuth(String appCode, String orgId) {
        // 首先智搭平台管理员
        List<String> orgIdsByRoleId = orgUtil.getOrgIdsByRoleId(platformRoleId);
        if (orgIdsByRoleId.contains(orgId)) {
            return true;
        }
        // 还需要一个 平台管理员的角色
        if (appCode.contains(",")) {
            // 假如是多个的话，那么 应该数量相等
            List<String> list = Arrays.asList(appCode.split(","));
            int count = dao.count(eqOrIn(ZdAppManager::getAppCode, list)
                    .eq(ZdAppManager::getOrgId, orgId)
                    .select(ZdAppManager::getId));
            return count == list.size();
        }
        return getBaseMapper().hasAuth(appCode, orgId);
    }

    @Override
    public boolean hasAuth(Long appId, String orgId) {
        String appCode = stringValue(LamWrapper.eqOrIn(ZdApp::getId, appId).select(ZdApp::getCode));
        return hasAuth(appCode, orgId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(ZdAppManagerDTO zdAppManagerDTO) {
        ZdAppManager zdAppManager;
        if (zdAppManagerDTO instanceof ZdAppManager) {
            zdAppManager = (ZdAppManager) zdAppManagerDTO;
        } else {
            zdAppManager = new ZdAppManager();
            BeanUtils.copyProperties(zdAppManagerDTO, zdAppManager);
        }
        return dao.save(zdAppManager);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(ZdAppManagerSaveDTO zdAppManagerSaveDTO) {
        List<String> orgIdList = zdAppManagerSaveDTO.getOrgIdList();
        if (CollectionUtils.isEmpty(orgIdList)) {
            return false;
        }
        String appCode = zdAppManagerSaveDTO.getAppCode();
        // 先删除，后新增
        delete(new AppDeleteEvent(null, appCode));
        List<ZdAppManager> managerList = orgIdList.stream().map(a -> {
            ZdAppManager zdAppManager = new ZdAppManager();
            zdAppManager.setOrgId(a);
            zdAppManager.setAppCode(appCode);
            return zdAppManager;
        }).collect(Collectors.toList());
        return dao.saveBatch(managerList);
    }


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_PAGE + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> appCodeList = appDeleteEvent.getCodeList();
        LambdaQueryWrapper<ZdAppManager> queryWrapper = appCodeQuery(appCodeList);
        // 删除应用管理员
        dao.remove(queryWrapper);
        log.info("删除应用管理员！");
    }

    @Override
    public void customize(ZdAppSettingDTO zdAppSettingDTO, String appCode) {
        List<ZdAppManagerDTO> appManagerList = zdAppSettingDTO.getAppManagerList();
        if (appManagerList == null) {
            appManagerList = new ArrayList<>();
            zdAppSettingDTO.setAppManagerList(appManagerList);
        } else {
            return;
        }
        List<ZdAppManager> list = dao.list(appCodeQuery(appCode).select(ZdAppManager::getOrgId));
        appManagerList.addAll(list);
    }

    private LambdaQueryWrapper<ZdAppManager> appCodeQuery(Object appCode) {
        return eqOrIn(ZdAppManager::getAppCode, appCode);
    }
}




