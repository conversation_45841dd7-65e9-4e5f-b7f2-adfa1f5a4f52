package com.sinitek.sirm.nocode.page.support.member;

import com.sinitek.sirm.nocode.page.support.member.base.AbstractMemberTest;
import com.sinitek.sirm.org.dto.EmployeeSearchDTO;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 部门成员
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class DepartmentMember extends AbstractMemberTest {

    @Override
    public void setParam(EmployeeSearchDTO employeeSearchDTO, String[] orgIds) {
        // 设置部门id
        employeeSearchDTO.setSelectDeptIdList(Arrays.asList(orgIds));
    }
}
