package com.sinitek.sirm.nocode.common.config;

import cn.hutool.core.util.ReflectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.cloud.base.support.CurrentUser;
import com.sinitek.cloud.base.support.TokenCheckInterceptor;
import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.sirm.application.support.ApplicationOpenApiSupport;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.i18n.interceptor.LocaleChangeInterceptor;
import com.sinitek.sirm.nocode.common.feign.IRemoteOrgUserService;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.user.service.IUserFlagTokenService;
import com.sinitek.spirit.um.server.shiro.IgnoreUrlUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0519
 * @description 当前用户信息拦截器, 仅仅本地调试用
 * @since 1.0.0-SNAPSHOT
 */

@Slf4j
@Order(Integer.MIN_VALUE)
@ConditionalOnProperty(prefix = "nocode", name = "local-authentication", havingValue = "true")
@RequiredArgsConstructor
@Configuration
public class TokenCreateInterceptor implements HandlerInterceptor, WebMvcConfigurer, SmartInitializingSingleton {

    @Lazy
    @Resource
    private IRemoteOrgUserService remoteOrgService;
    @Lazy
    @Resource
    private IUserFlagTokenService userFlagTokenService;
    @Lazy
    @Resource
    private IOrgService orgService;


    @Resource
    private TokenCheckInterceptor tokenCheckInterceptor;

    private final ApplicationOpenApiSupport applicationOpenApiSupport;
    private final IgnoreUrlUtil ignoreUrlUtil;


    @Value("${sirm.security.path:/**}")
    private String path;
    @Value("${sirm.security.excludepath:}")
    private String excludepath;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            boolean isOpenApi = this.applicationOpenApiSupport.isOpenApiAndInternalIdentification(handlerMethod);
            if (isOpenApi) {
                return true;
            }
        } else {
            // 资源的话，不拦截
            return true;
        }

        String accountId = request.getHeader("accountId");
        if (StringUtils.isNotBlank(accountId)) {
            Employee employee = orgService.getEmployeeById(accountId);
            if (Objects.nonNull(employee)) {
                UserDTO user = new UserDTO();
                user.setUsename(employee.getUserName());
                user.setUserid(employee.getUserId());
                user.setOrgid(employee.getId());
                user.setOrgname(employee.getEmpName());
                user.setLocale("zh_CN");
                log.debug("current user = {}", user);
                CurrentUser.begin();
                CurrentUser.setRequest(request);
                CurrentUser.init(user);
                return true;
            }
        }

        String accessTokenByRequest = getAccessTokenByRequest(request);
        if (StringUtils.isNotBlank(accessTokenByRequest)) {
            UserDTO user = checkToken(accessTokenByRequest);
            if (user != null) {
                String responseAccessToken = user.getResponseAccessToken();
                if (StringUtils.isNotBlank(responseAccessToken) && !Objects.equals(responseAccessToken, accessTokenByRequest)) {
                    response.setHeader("accesstoken", responseAccessToken);
                }
                log.debug("current user = {}", user);
                CurrentUser.begin();
                CurrentUser.setRequest(request);
                CurrentUser.init(user);
                return true;
            } else {
                return fail(response);
            }
        } else {
            log.info("接口: {} 中的accesstoken为空", SpringMvcUtil.getRequestUri());
        }

        log.debug("微服务调用验证失败，无效的token");
        Map<String, String> res = new HashMap<>();
        res.put("result", "010110");
        res.put("message", "API Token Forbidden or Expired");
        ObjectMapper mapper = new ObjectMapper();
        String subject = "";
        try {
            subject = mapper.writeValueAsString(res);
        } catch (JsonProcessingException e) {
            log.warn("JSON转换异常", e);
            subject = "API Token Forbidden or Expired";
        }
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.getWriter().println(subject);
        return false;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CurrentUser.end();
    }


    public String getAccessTokenByRequest(HttpServletRequest request) {
        if (request == null) {
            return "";
        } else {
            String token = request.getHeader("accesstoken");
            if ("null".equalsIgnoreCase(token)) {
                token = "";
            }

            if (StringUtils.isNotBlank(token)) {
                return token;
            } else {
                token = request.getParameter("accesstoken");
                if (StringUtils.isBlank(token)) {
                    Cookie[] cookies = request.getCookies();
                    if (cookies != null) {
                        for (Cookie cookie : cookies) {
                            if ("accesstoken".equals(cookie.getName())) {
                                token = cookie.getValue();
                                break;
                            }
                        }
                    }

                }

                return StringUtils.isNotBlank(token) ? token : "";
            }
        }
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        LocaleChangeInterceptor localeChangeInterceptor = (LocaleChangeInterceptor) SpringFactory.getBean(LocaleChangeInterceptor.class);
        if (localeChangeInterceptor != null) {
            registry.addInterceptor(localeChangeInterceptor).order(21).addPathPatterns("/**");
        }

        List<String> ignoreUrlList = this.ignoreUrlUtil.getIgnoreUrlList();
        InterceptorRegistration registration = registry.addInterceptor(this);
        registration.addPathPatterns(org.apache.commons.lang.StringUtils.split(this.path, ","));
        registration.excludePathPatterns(ignoreUrlList);
        if (org.apache.commons.lang.StringUtils.isNotBlank(this.excludepath)) {
            registration.excludePathPatterns(org.apache.commons.lang.StringUtils.split(this.excludepath, ","));
        }

    }

    private boolean fail(HttpServletResponse response) throws Exception {
        RequestResult<Object> requestResult = RequestResult.fail("0000066");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        ObjectMapper mapper = new ObjectMapper();
        String subject = mapper.writeValueAsString(requestResult);
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.getWriter().println(subject);

        return false;
    }

    @Override
    public void afterSingletonsInstantiated() {
        // 让本拦截器接管  tokenCheckInterceptor
        ReflectUtil.setFieldValue(tokenCheckInterceptor, "inCloud", "false");
    }


    public UserDTO checkToken(String accessToken) {
        ResponseEntity<RequestResult<UserDTO>> response = this.remoteOrgService.current(accessToken);
        RequestResult<UserDTO> result = (RequestResult) response.getBody();
        if (result != null && result.isSuccess()) {
            UserDTO user = (UserDTO) result.getData();
            log.debug("current = {}", user);
            String respAccessToken = response.getHeaders().getFirst("accesstoken");
            if (org.apache.commons.lang.StringUtils.isNotBlank(respAccessToken)) {
                user.setResponseAccessToken(respAccessToken);
            }

            return user;
        } else {
            log.error("远调获取当前用户失败, accessToken: {}", accessToken);
            return null;
        }
    }
}
