package com.sinitek.sirm.nocode.page.support.member.base;

import com.sinitek.sirm.org.entity.Employee;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */

public interface MemberTest {
    /**
     * 测试当前用户是否有权限
     *
     * @param currentOrgId 当前用户组织id
     * @param orgIds       有权限的组织id
     * @param findOrgIdSet 获取到的人员id集合
     * @param employeeList 人员详细信息
     * @return true:可以访问，false:不可以访问
     */
    boolean test(String currentOrgId, String[] orgIds, Set<String> findOrgIdSet, List<Employee> employeeList);
}
