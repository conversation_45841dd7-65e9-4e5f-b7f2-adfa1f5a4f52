package com.sinitek.sirm.nocode.common.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.cloud.common.utils.JWTUtils;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.common.support.thread.ContextThreadCleaner;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 2025.0507
 * @since 1.0.0-SNAPSHOT
 * 智搭应用鉴权拦截器
 */
@Component
@Slf4j
public class ApplicationTokenInterceptor implements HandlerInterceptor {
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            String token = request.getHeader(ZdCommonConstant.APPLICATION_TOKEN);
            // 没有apiToken 的情况下，并且存在 应用的token
            if (StringUtils.isNotBlank(token)) {
                try {
                    Claims claims = JWTUtils.parseJwt(token);
                    if (claims != null) {
                        RequestContext.begin();
                        RequestContext.init(objectMapper.readValue(claims.getSubject(), String.class));
                        return true;
                    }
                } catch (Exception e) {
                    log.error("接口: {},{}: {}, 解码失败", SpringMvcUtil.getRequestUri(), ZdCommonConstant.APPLICATION_TOKEN, token, e);
                }
            } else {
                log.info("接口: {} 中的{}为空", SpringMvcUtil.getRequestUri(), ZdCommonConstant.APPLICATION_TOKEN);
            }
        }
        log.debug("微服务调用验证失败，无效的应用token");
        String subject = "{\"result\":\"010110\",\"message\":\"APP Token Forbidden or Expired\"}";
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.getWriter().println(subject);
        return false;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ContextThreadCleaner.cleanAll();
    }
}
