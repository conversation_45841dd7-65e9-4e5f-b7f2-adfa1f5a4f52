package com.sinitek.sirm.nocode.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import org.springframework.context.annotation.Configuration;

/**
 * 自定义Jackson配置
 *
 * <AUTHOR>
 * @version 2025.0521
 * @since 1.0.0-SNAPSHOT
 */
@Configuration
public class CustomJackJsonConfig {
    public CustomJackJsonConfig(ObjectMapper objectMapper) {
        // java 8 时间处理器
        objectMapper.registerModule(new JavaTimeModule());
        JsonbTypeHandler.setObjectMapper(objectMapper);
    }
}
