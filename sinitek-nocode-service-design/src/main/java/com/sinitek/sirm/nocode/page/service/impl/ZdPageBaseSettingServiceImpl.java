package com.sinitek.sirm.nocode.page.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.utils.CopyUtil;
import com.sinitek.sirm.nocode.page.dao.ZdPageBaseSettingDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPageBaseSettingDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageBaseSetting;
import com.sinitek.sirm.nocode.page.mapper.ZdPageBaseSettingMapper;
import com.sinitek.sirm.nocode.page.service.IZdPageBaseSettingService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025-03-12 13:19:31
 * @description 针对表【zd_page_base_setting(页面的基本设置表)】的数据库操作Service实现
 */
@Service
public class ZdPageBaseSettingServiceImpl extends BaseDAO<ZdPageBaseSettingMapper, ZdPageBaseSetting, ZdPageBaseSettingDAO>
        implements IZdPageBaseSettingService {


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> pageCodeList = appDeleteEvent.getCodeList();
        LambdaQueryWrapper<ZdPageBaseSetting> queryWrapper = pageCodeQuery(pageCodeList);
        // 删除页面配置
        dao.remove(queryWrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdate(ZdPageBaseSettingDTO zdPageBaseSettingDTO) {
        ZdPageBaseSetting zdPageBaseSetting = idCheck(zdPageBaseSettingDTO.getId());
        if (Objects.isNull(zdPageBaseSetting)) {
            zdPageBaseSetting = (ZdPageBaseSetting) getByPageCode(zdPageBaseSettingDTO.getFormCode());
        }
        if (Objects.isNull(zdPageBaseSetting)) {
            zdPageBaseSetting = new ZdPageBaseSetting();
        }
        CopyUtil.copyEntityProperties(zdPageBaseSettingDTO, zdPageBaseSetting);
        return dao.saveOrUpdate(zdPageBaseSetting);
    }

    @Override
    public ZdPageBaseSettingDTO getByPageCode(String pageCode) {
        return dao.getOne(pageCodeQuery(Collections.singletonList(pageCode)));
    }

    private LambdaQueryWrapper<ZdPageBaseSetting> pageCodeQuery(List<String> pageCodeList) {
        return eqOrIn(ZdPageBaseSetting::getFormCode, pageCodeList);

    }
}




