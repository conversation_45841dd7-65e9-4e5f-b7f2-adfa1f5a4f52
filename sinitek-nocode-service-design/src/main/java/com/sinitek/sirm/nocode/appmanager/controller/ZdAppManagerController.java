package com.sinitek.sirm.nocode.appmanager.controller;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dto.ZdAppManagerSaveDTO;
import com.sinitek.sirm.nocode.app.service.IZdAppManagerService;
import com.sinitek.sirm.nocode.appmanager.aspect.ZdAppRight;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/app-manager", tags = "应用管理员接口")
@RequestMapping("/frontend/api/nocode/app-manager")
public class ZdAppManagerController {

    @Resource
    private IZdAppManagerService zdAppManagerService;


    @ZdAppRight(AppConstant.APP_CODE_PARAM)
    @ApiOperation(value = "保存应用管理员接口")
    @PostMapping("/save")
    public RequestResult<Boolean> save(
            @ApiParam(name = "保存应用管理员", value = "保存应用管理员", required = true)
            @RequestBody ZdAppManagerSaveDTO zdAppManagerSaveDTO
    ) {
        zdAppManagerSaveDTO.setCurrentOrgId(CurrentUserFactory.getOrgId());
        return new RequestResult<>(zdAppManagerService.save(zdAppManagerSaveDTO));
    }
}
