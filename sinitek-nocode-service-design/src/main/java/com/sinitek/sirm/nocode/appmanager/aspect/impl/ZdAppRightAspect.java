package com.sinitek.sirm.nocode.appmanager.aspect.impl;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.service.IZdAppManagerService;
import com.sinitek.sirm.nocode.app.service.IZdAppService;
import com.sinitek.sirm.nocode.app.support.AppCodeSupplier;
import com.sinitek.sirm.nocode.appmanager.aspect.ZdAppRight;
import com.sinitek.sirm.nocode.common.utils.SpringMethodElUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0601
 * @description 应用权限切面
 * @since 1.0.0-SNAPSHOT
 */
@Component
@Aspect
@Slf4j
public class ZdAppRightAspect {

    @Resource
    private IZdAppService appService;

    @Resource
    private IZdAppManagerService appManagerService;

    @Resource
    private SpringMethodElUtil parseSpringEl;

    @Pointcut("@annotation(appRight)")
    public void aspect(ZdAppRight appRight) {
        // 切面
    }


    @Before(value = "aspect(appRight)", argNames = "joinPoint,appRight")
    public void setDataRight(JoinPoint joinPoint, ZdAppRight appRight) {
        String orgId = CurrentUserFactory.getOrgId();
        if (StringUtils.isBlank(orgId)) {
            // 没有登录
            throw new BussinessException("3000006");
        }
        String appCode = findAppCode(joinPoint, appRight);
        if (StringUtils.isBlank(appCode) || !appService.exists(appCode)) {
            // 找不到相关应用
            throw new BussinessException("3000007");
        }

        if (appRight.checkStatus() && !appService.isEnable(appCode)) {
            // 应用已经被禁用！
            //throw new BussinessException("3000017");
            log.info("应用被禁用！appCode:{}", appCode);

        }

        if (!appManagerService.hasAuth(appCode, orgId)) {
            String messageCode = appRight.messageCode();
            if (StringUtils.isBlank(messageCode)) {
                // 没有该应用的权限
                messageCode = "3000002";
            }
            throw new BussinessException(messageCode);
        }
    }

    /**
     * 获取应用编码
     *
     * @param joinPoint 切点
     * @param appRight  权限
     * @return 应用编码
     * @since v1.0.0.x
     */
    private String findAppCode(JoinPoint joinPoint, ZdAppRight appRight) {
        String key = appRight.value();
        if (parseSpringEl.isSpringEl(key)) {
            return parseSpringEl.value(key, (ProceedingJoinPoint) joinPoint);
        }
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg instanceof AppCodeSupplier) {
                return ((AppCodeSupplier) arg).getCode();
            }
        }
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        // 从注解里面获取
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
            if (Objects.nonNull(requestParam) && Objects.equals(AppConstant.APP_CODE, requestParam.value())) {
                return (String) args[i];
            }
        }
        // 从参数名称里面获取
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            // 参数名称，需要打包支持
            String name = parameter.getName();
            if (Objects.equals(AppConstant.APP_CODE, name)) {
                return (String) args[i];
            }
        }

        if (args.length == 1 && args[0] instanceof String) {
            return (String) args[0];
        }
        // todo
        throw new BussinessException("");

    }
}
