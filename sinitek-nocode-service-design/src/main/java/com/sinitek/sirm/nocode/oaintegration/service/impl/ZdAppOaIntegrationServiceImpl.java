package com.sinitek.sirm.nocode.oaintegration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dao.ZdAppOaIntegrationDAO;
import com.sinitek.sirm.nocode.app.dto.ZdAppOaIntegrationDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSettingDTO;
import com.sinitek.sirm.nocode.app.entity.ZdAppOaIntegration;
import com.sinitek.sirm.nocode.app.enumerate.PlatformTypeEnum;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.app.mapper.ZdAppOaIntegrationMapper;
import com.sinitek.sirm.nocode.app.service.IZdAppOaIntegrationService;
import com.sinitek.sirm.nocode.app.support.IZdAppSettingCustomizer;
import com.sinitek.sirm.nocode.oaintegration.support.login.base.IPlatformLogin;
import com.sinitek.sirm.nocode.support.BaseDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025-03-12 09:24:24
 * @description 针对表【zd_app_oa_integration(应用OA集成表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class ZdAppOaIntegrationServiceImpl extends BaseDAO<ZdAppOaIntegrationMapper, ZdAppOaIntegration, ZdAppOaIntegrationDAO>
        implements IZdAppOaIntegrationService, IZdAppSettingCustomizer {


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_PAGE + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> appCodeList = appDeleteEvent.getCodeList();
        // 删除oa集成
        dao.remove(appCodeQuery(appCodeList));
        log.info("删除oa集成");
    }

    @Override
    public void customize(ZdAppSettingDTO zdAppSettingDTO, String appCode) {
        ZdAppOaIntegration one = dao.getOne(appCodeQuery(appCode));
        zdAppSettingDTO.setAppOaIntegration(Objects.isNull(one) ? new ZdAppOaIntegration() : one);
    }

    private LambdaQueryWrapper<ZdAppOaIntegration> appCodeQuery(Object appCodeList) {
        return eqOrIn(ZdAppOaIntegration::getAppCode, appCodeList);
    }

    @Override
    public boolean saveOrUpdate(ZdAppOaIntegrationDTO zdAppOaIntegrationDTO) {
        String appCode = zdAppOaIntegrationDTO.getAppCode();
        LambdaQueryWrapper<ZdAppOaIntegration> query = appCodeQuery(appCode);
        ZdAppOaIntegration one = dao.getOne(query);
        if (Objects.isNull(one)) {
            one = new ZdAppOaIntegration();
        }
        one.setPlatformType(zdAppOaIntegrationDTO.getPlatformType());
        one.setParam(zdAppOaIntegrationDTO.getParam());
        one.setAppCode(appCode);
        return dao.saveOrUpdate(one);
    }

    @Override
    public String login(String appCode, String code, HttpServletRequest request, HttpServletResponse response) {
        LambdaQueryWrapper<ZdAppOaIntegration> query = appCodeQuery(appCode);
        ZdAppOaIntegration one = dao.getOne(query);
        if (Objects.isNull(one)) {
            throw new BussinessException("");
        }
        PlatformTypeEnum platformType = one.getPlatformType();
        IPlatformLogin platformLogin = SpringFactory.getBean(platformType.beanName("PlatformLogin"), IPlatformLogin.class);
        return platformLogin.login(request, response, one.getParam(), code, appCode);

    }
}




