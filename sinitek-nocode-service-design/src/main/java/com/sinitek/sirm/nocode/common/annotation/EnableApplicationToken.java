package com.sinitek.sirm.nocode.common.annotation;

import com.sinitek.sirm.nocode.common.config.ApplicationTokenWebConfig;
import com.sinitek.sirm.nocode.common.properties.NocodeSecurityProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 2025.0507
 * @since 1.0.0-SNAPSHOT
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Import({ApplicationTokenWebConfig.class})
@EnableConfigurationProperties({NocodeSecurityProperties.class})
public @interface EnableApplicationToken {
}
