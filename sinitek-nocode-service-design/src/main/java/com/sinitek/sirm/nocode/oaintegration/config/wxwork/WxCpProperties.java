package com.sinitek.sirm.nocode.oaintegration.config.wxwork;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025.0427
 * @description 微信配置
 * @since 1.0.0-SNAPSHOT
 */
@ConfigurationProperties(
        prefix = "sinicube.wxwork"
)
@Data
@ApiModel(description = "企业微信配置")
public class WxCpProperties {
    @ApiModelProperty(value = "企业微信id", required = true, example = "wwe557b31f8d5de815")
    private String corpId;
    @ApiModelProperty(value = "应用配置")
    private Map<String, AppConfig> appConfigs;


    @ApiModel(description = "企业应用配置")
    @Data
    public static class AppConfig {
        /**
         * <a href="https://work.weixin.qq.com/wework_admin/frame#apps/modApiApp/5629501431723928">应用测试</a>
         */
        @ApiModelProperty(value = "应用id", required = true, example = "1000003")
        private Integer agentId;
        @ApiModelProperty(value = "应用密钥", required = true, example = "acp6bye0-mVv_h1opKLv_ZeWs6Lp2EULDfHZ41hFzPc")
        private String secret;
        /**
         * <a href="https://work.weixin.qq.com/wework_admin/frame#apps/modApiApp/5629501431723928">设置API接收</a>
         */
        @ApiModelProperty(value = "token", example = "cMphujK6CIrorM")
        private String token;
        @ApiModelProperty(value = "aesKey", example = "psrFGVj3CgTdSm9GwJbPhGtUnn8rLEclWJ0s5vRyfSR")
        private String aesKey;
    }
}