package com.sinitek.sirm.nocode.page.support.member;

import com.sinitek.sirm.nocode.page.support.member.base.MemberTest;
import com.sinitek.sirm.org.entity.Employee;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 所有成员
 *
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class AllMember implements MemberTest {
    @Override
    public boolean test(String currentOrgId, String[] orgIds, Set<String> findOrgIdSet, List<Employee> employeeList) {
        return true;
    }
}
