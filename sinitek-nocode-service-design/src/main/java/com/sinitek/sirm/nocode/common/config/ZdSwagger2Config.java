package com.sinitek.sirm.nocode.common.config;

import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.common.support.swagger.SwaggerEnumBuilderPlugin;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * <AUTHOR>
 * @date 11/23/2022 1:09 PM
 */
@Configuration
@EnableSwagger2WebMvc
@Slf4j
public class ZdSwagger2Config {

    @Bean
    public Docket createLcRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName(ZdCommonConstant.NAME)
                .apiInfo(lcApiInfo())
                .select()
                //此包路径下的类，才生成接口文档
                .apis(RequestHandlerSelectors.basePackage("com.sinitek.sirm.nocode"))
                //加了ApiOperation注解的类，才生成接口文档
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo lcApiInfo() {
        return new ApiInfoBuilder()
                .title("SiniCube 零代码平台 APIs")
                .version("1.x")
                .contact(new Contact("基础框架团队", "http://www.sinitek.com/", ""))
                .build();
    }

    /**
     * 枚举参数处理
     */
    @Bean
    public SwaggerEnumBuilderPlugin swaggerEnumBuilderPlugin() {
        return new SwaggerEnumBuilderPlugin();
    }
}
