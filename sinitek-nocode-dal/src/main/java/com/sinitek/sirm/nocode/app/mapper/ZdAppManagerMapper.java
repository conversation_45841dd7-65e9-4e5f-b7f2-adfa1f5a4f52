package com.sinitek.sirm.nocode.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.sirm.nocode.app.entity.ZdAppManager;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 2025-03-12 09:07:47
 * @description 针对表【zd_app_manager(应用管理员)】的数据库操作Mapper
 * @Entity generator.domain.ZdAppManager
 */
public interface ZdAppManagerMapper extends BaseMapper<ZdAppManager> {

    /**
     * 判断是否存在
     *
     * @param appCode 应用编码
     * @param orgId   登陆人的组织编码
     * @return 是存在
     */
    boolean hasAuth(@Param("appCode") String appCode, @Param("orgId") String orgId);


}




