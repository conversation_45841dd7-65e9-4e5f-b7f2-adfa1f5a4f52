package com.sinitek.sirm.nocode.support.mybatis.util;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.base.ResultType;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.builder.IncompleteElementException;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ResultMap;
import org.apache.ibatis.session.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0420
 * @description MappedStatement 制作者
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class MappedStatementMakerUtil {
    /**
     * 缓存
     */
    private static final Map<String, List<ResultMap>> RESULT_MAP_CACHE = new HashMap<>();

    private final Configuration configuration;
    private final MappedStatement mappedStatement;

    private final String statementId;

    private String resultMap;
    private Class<?> resultType;

    private MappedStatementMakerUtil(MappedStatement mappedStatement, Object returnTypeObject) {

        this.mappedStatement = mappedStatement;
        this.configuration = mappedStatement.getConfiguration();

        if (returnTypeObject instanceof String) {
            resultMap = (String) returnTypeObject;
        } else if (returnTypeObject instanceof Class) {
            resultType = (Class<?>) returnTypeObject;
        }

        statementId = mappedStatement.getId();
    }


    public static MappedStatement copy(MappedStatement mappedStatement, Object returnType) {
        return new MappedStatementMakerUtil(mappedStatement, returnType).make();
    }

    public static List<ResultMap> getResultMaps(MappedStatement mappedStatement, Object returnTypeObject) {
        return new MappedStatementMakerUtil(mappedStatement, returnTypeObject).getStatementResultMaps();
    }

    private MappedStatement make() {
        return copyMappedStatement();
    }


    /**
     * 复制一个 MappedStatement
     *
     * @return 新的 mapper描述
     */
    protected MappedStatement copyMappedStatement() {
        MappedStatement.Builder builder = new MappedStatement.Builder(configuration, statementId, mappedStatement.getSqlSource(), mappedStatement.getSqlCommandType());
        return copyMappedStatement(builder)
                .resultMaps(getStatementResultMaps())
                .build();
    }

    /**
     * 复制 mappedStatement
     *
     * @param builder 构造器
     * @return builder
     */

    protected MappedStatement.Builder copyMappedStatement(MappedStatement.Builder builder) {
        return builder.resource(mappedStatement.getResource())
                .fetchSize(mappedStatement.getFetchSize())
                .timeout(mappedStatement.getTimeout())
                .statementType(mappedStatement.getStatementType())
                .keyGenerator(mappedStatement.getKeyGenerator())
                .keyProperty(getStringValue(mappedStatement.getKeyProperties()))
                .keyColumn(getStringValue(mappedStatement.getKeyColumns()))
                .databaseId(mappedStatement.getDatabaseId())
                .lang(mappedStatement.getLang())
                .resultOrdered(mappedStatement.isResultOrdered())
                .resultSets(getStringValue(mappedStatement.getResultSets()))
                .resultSetType(mappedStatement.getResultSetType())
                .flushCacheRequired(mappedStatement.isFlushCacheRequired())
                .parameterMap(mappedStatement.getParameterMap())
                .useCache(mappedStatement.isUseCache())
                .cache(mappedStatement.getCache());
    }

    /**
     * 将 数组转化为 字符串
     *
     * @param value 数组
     * @return String
     */
    private static String getStringValue(String[] value) {
        if (value != null && value.length > 0) {
            return String.join(",", value);
        }
        return null;

    }

    private List<ResultMap> getStatementResultMaps() {
        List<ResultMap> resultMaps = new ArrayList<>();
        if (resultMap != null) {
            String[] resultMapNames = resultMap.split(",");
            for (String resultMapName : resultMapNames) {
                try {
                    resultMaps.add(configuration.getResultMap(resultMapName.trim()));
                } catch (IllegalArgumentException e) {
                    log.error(e.getMessage(),e);
                    throw new IncompleteElementException("Could not find result map '" + resultMapName + "' referenced from '" + statementId + "'", e);
                }
            }
        } else if (resultType != null) {
            return RESULT_MAP_CACHE.computeIfAbsent(resultType.getName(), k -> find(resultType, statementId));
        }
        return resultMaps;
    }

    private List<ResultMap> find(Class<?> resultType, String statementId) {
        List<ResultMap> resultMaps = new ArrayList<>();
        TableInfo tableInfo = TableInfoHelper.getTableInfo(resultType);
        ResultMap inlineResultMap = null;
        if (Objects.nonNull(tableInfo)&&StringUtils.isNotBlank(tableInfo.getResultMap())) {
            inlineResultMap = configuration.getResultMap(tableInfo.getResultMap());
        }
        if (inlineResultMap == null) {
            inlineResultMap = new ResultMap.Builder(
                    configuration,
                    statementId + "-Inline",
                    resultType,
                    new ArrayList<>(),
                    null).build();
        }
        resultMaps.add(inlineResultMap);
        return resultMaps;
    }


    /**
     * 定制化 resultMap
     *
     * @param mappedStatement mappedStatement
     * @param boundSql        boundSql
     * @return resultMap
     */
    @SuppressWarnings("unchecked")
    public static List<ResultMap> getResultMaps(MappedStatement mappedStatement, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        if (parameterObject instanceof MapperMethod.ParamMap) {
            MapperMethod.ParamMap<Object> paramMap = (MapperMethod.ParamMap<Object>) parameterObject;
            if (paramMap.containsKey(Constants.WRAPPER)) {
                Object o = paramMap.get(Constants.WRAPPER);
                if (o instanceof ResultType) {
                    ResultType r = (ResultType) o;
                    Object resultClassType = r.getReturnType();
                    if (resultClassType != null) {
                        return getResultMaps(mappedStatement, resultClassType);
                    }
                }
            }
        }
        return mappedStatement.getResultMaps();
    }
}
