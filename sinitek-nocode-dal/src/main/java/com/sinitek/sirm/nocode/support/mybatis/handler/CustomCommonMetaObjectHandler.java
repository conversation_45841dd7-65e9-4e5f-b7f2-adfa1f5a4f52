package com.sinitek.sirm.nocode.support.mybatis.handler;

import com.sinitek.data.mybatis.handler.CommonMetaObjectHandler;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.nocode.common.mybaits.CustomBaseAudit;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * 多继承的话，就用不了 BaseAuditEntity 了
 *
 * <AUTHOR>
 * @version 2025.0630
 * @since 1.0.0-SNAPSHOT
 */
@Component
@Primary
public class CustomCommonMetaObjectHandler extends CommonMetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        super.insertFill(metaObject);
        Object originalObject = metaObject.getOriginalObject();
        if (originalObject instanceof CustomBaseAudit) {
            String orgId = CurrentUserFactory.getOrgId();
            CustomBaseAudit customBaseAudit = (CustomBaseAudit) originalObject;
            customBaseAudit.setCreatorId(orgId);
            customBaseAudit.setUpdaterId(orgId);
        }
    }


    @Override
    public void updateFill(MetaObject metaObject) {
        super.updateFill(metaObject);
        Object originalObject = metaObject.getOriginalObject();
        if (originalObject instanceof CustomBaseAudit) {
            String orgId = CurrentUserFactory.getOrgId();
            CustomBaseAudit customBaseAudit = (CustomBaseAudit) originalObject;
            customBaseAudit.setUpdaterId(orgId);
        }
    }
}
