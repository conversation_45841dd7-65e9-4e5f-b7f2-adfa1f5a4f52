package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * 表单配置表
 *
 * @TableName zd_page_form_config
 */
@Data
@ApiModel(description = "表单配置实体")
@TableName(value = "zd_page_form_config")
public class ZdPageFormConfig implements Serializable {

    @TableId("ID")
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;

    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码", example = "a10000")
    private String formCode;

    /**
     * 数据表的名称,当为普通表单或者是流程表单时，这个字段有值
     */
    @ApiModelProperty(value = "数据表的名称,当为普通表单或者是流程表单时，这个字段有值", example = "zd_page_form_data_0")
    private String tableName;

    /**
     * 当表单为流程表单时，这个字段有值
     */
    @ApiModelProperty(value = "当表单为流程表单时，这个字段有值", example = "yearWorkFlow")
    private String processcode;

    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}