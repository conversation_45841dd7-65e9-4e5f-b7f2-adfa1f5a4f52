package com.sinitek.sirm.nocode.form.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.sirm.nocode.form.entity.ZdPageForm;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 2025-03-12 11:04:49
 * @description 针对表【zd_page_form(页面表单表)】的数据库操作Mapper
 * @Entity com.sinitek.sirm.nocode.form.entity.ZdPageForm
 */
public interface ZdPageFormMapper extends BaseMapper<ZdPageForm> {

    /**
     * 根据表单编码和应用编码判断表单是否有关系
     *
     * @param formCode 表单编码
     * @param appCode  应用编码
     * @return 是否存在
     */
    Boolean codeBelongsToAppCode(@Param("formCode") String formCode, @Param("appCode") String appCode);

    /**
     * 根据表单ID获取应用编码
     *
     * @param formId 表单ID
     * @return 应用编码
     */
    String getAppCodeByFormId(@Param("formId") Long formId);
}




