package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.page.dto.ZdPageSceneDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 页面场景设置
 *
 * @TableName zd_page_scene
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_scene", autoResultMap = true)
@Data
public class ZdPageScene extends ZdPageSceneDTO {
    @JsonIgnore
    @ApiModelProperty("下一个任务执行日期")
    private LocalDate nextExecuteDate;
    @JsonIgnore
    @ApiModelProperty("完成日期")
    private LocalDate finishDate;
}
