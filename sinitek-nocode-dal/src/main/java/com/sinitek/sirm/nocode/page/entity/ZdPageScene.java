package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.common.support.handler.ListIntegerTypeHandler;
import com.sinitek.sirm.nocode.common.support.handler.ListStringTypeHandler;
import com.sinitek.sirm.nocode.page.enumerate.NoteTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneRepeatTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneSubmitEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 页面场景设置
 *
 * @TableName zd_page_scene
 */
@Data
@ApiModel(description = "场景实体")
@TableName(value = "zd_page_scene", autoResultMap = true)
public class ZdPageScene implements Serializable {

    @TableId("ID")
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;

    /**
     * 页面编码
     */
    @JsonIgnore
    @NotBlank(message = "页面编码不能为空")
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String pageCode;

    /**
     * 场景类型
     */
    @ApiEnumProperty(example = "0")
    private SceneTypeEnum sceneType;

    /**
     * 提交类型，0：无限制，1：每个人限制一次
     */
    @ApiEnumProperty(example = "0")
    private SceneSubmitEnum submitType;

    /**
     * 重复类型，0：每天，1：每周，2：每月
     */
    @ApiEnumProperty(example = "0")
    private SceneRepeatTypeEnum repeatType;

    /**
     * 每天开始时间
     */
    @JsonFormat(pattern = "HH:mm")
    @ApiModelProperty(value = "每天开始时间", example = "09:40")
    private LocalTime startTime;

    /**
     * 是否跳过法定节假日
     */
    @ApiModelProperty(value = "是否跳过节假日,0:不跳过，1：跳过,数值类型", example = "0")
    @ApiEnumProperty(description = "是否跳过节假日", viewEnum = false, example = "0")
    private YesOrNoEnum holidayStrategy;

    /**
     * 截至时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "表单收集截至时间", example = "2025-03-25")
    private LocalDate endDate;

    /**
     * 报告发送人，多个用逗号隔开
     */
    @TableField(typeHandler = ListStringTypeHandler.class)
    @ApiModelProperty(value = "报告发送人，存储人员id,是个人员id数组", example = "[121212,11111]")
    private List<String> reportSendOrgIds;

    @TableField(typeHandler = ListIntegerTypeHandler.class)
    @ApiEnumProperty(enumClazz = NoteTypeEnum.class, keepDataType = true, example = "[0]")
    private List<Integer> noteType;

    @JsonIgnore
    @ApiModelProperty("下一个任务执行日期")
    private LocalDate nextExecuteDate;

    @JsonIgnore
    @ApiModelProperty("完成日期")
    private LocalDate finishDate;

    @ApiModelProperty(value = "表单编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    public void setFormcode(String formcode) {
        this.pageCode = formcode;
    }

    @NotBlank(message = "表单编码不能为空")
    public String getFormcode() {
        return this.pageCode;
    }

    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}
