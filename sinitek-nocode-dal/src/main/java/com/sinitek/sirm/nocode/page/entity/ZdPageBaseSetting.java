package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.support.validator.ValidatorAll;
import com.sinitek.sirm.nocode.page.enumerate.SubmitJumpTypeEnum;
import com.sinitek.sirm.nocode.page.support.validator.ZdPageBaseSettingValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * 页面的基本设置表
 *
 * @TableName zd_page_base_setting
 */
@Data
@ApiModel(description = "页面的基本设置实体")
@TableName(value = "zd_page_base_setting")
@ValidatorAll(value = ZdPageBaseSettingValidator.class)
public class ZdPageBaseSetting implements Serializable {

    @TableId("ID")
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;

    /**
     * 页面编码
     */
    @NotBlank(message = "页面编码不能为空")
    @TableField("page_code")
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String formCode;

    /**
     * 页面提交后跳转参数
     */
    @ApiModelProperty(value = "页面提交后跳转参数,应该是一个json数组", example = "\"[{name:\"id\",type:0}]\"")
    private String jumpParam;

    /**
     * 页面提交后跳转的类型，有三种，0:跳转默认页面，1：应用内页面，2:外部链接
     */
    @NotNull(message = "页面提交后跳转的类型不能为空")
    @ApiEnumProperty(required = true, example = "0")
    private SubmitJumpTypeEnum submitJumpType;

    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}
