package com.sinitek.sirm.nocode.form.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-21 18:28:16
 * @description 针对表【zd_page_form_config(表单配置表)】的数据库操作Mapper
 * @Entity com.sinitek.sirm.nocode.form.entity.ZdPageFormConfig
 */
public interface ZdPageFormConfigMapper extends BaseMapper<ZdPageFormConfig> {

    /**
     * 获取空闲的表名
     *
     * @return 所有空闲的表名称
     */
    List<String> findIdleTableNames();

    /**
     * 获取空闲表的数量
     *
     * @return 空闲表的数量
     */
    Integer getIdleTableLength();

    String getTableNameByFormCode(String formCode);


}




