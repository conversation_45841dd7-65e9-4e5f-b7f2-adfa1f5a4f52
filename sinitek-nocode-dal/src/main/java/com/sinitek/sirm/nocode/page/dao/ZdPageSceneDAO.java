package com.sinitek.sirm.nocode.page.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.nocode.page.entity.ZdPageScene;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import com.sinitek.sirm.nocode.page.mapper.ZdPageSceneMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0407
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@Repository
public class ZdPageSceneDAO extends ServiceImpl<ZdPageSceneMapper, ZdPageScene> {
    /**
     * 查询当天需要发送的报表
     *
     * @return 场景
     */
    public List<ZdPageScene> findNowDayReport() {
        LambdaQueryWrapper<ZdPageScene> queryWrapper = Wrappers.<ZdPageScene>lambdaQuery()
                .eq(ZdPageScene::getSceneType, SceneTypeEnum.COLLECTION_FORM)
                .eq(ZdPageScene::getFinishDate, LocalDate.now())
                .select(ZdPageScene::getId, ZdPageScene::getReportSendOrgIds, ZdPageScene::getPageCode, ZdPageScene::getNoteType);
        return list(queryWrapper);
    }
}
