package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面表
 *
 * @TableName zd_page
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page")
@Data
public class ZdPage extends ZdPageDTO {
    /**
     * 跟踪id
     */
    @ApiModelProperty(value = "跟踪id,来源于本表主键", example = "100000")
    private Long threadId;

}
