package com.sinitek.sirm.nocode.page.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.sirm.nocode.page.entity.ZdPageScene;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-12 14:21:01
 * @description 针对表【zd_page_scene(页面场景设置)】的数据库操作Mapper
 * @Entity com.sinitek.sirm.nocode.page.entity.ZdPageScene
 */
public interface ZdPageSceneMapper extends BaseMapper<ZdPageScene> {

    /**
     * 查询任务
     *
     * @param date 日期
     * @return 场景列表
     */
    List<ZdPageScene> findTask(@Param("date") LocalDate date);

}




