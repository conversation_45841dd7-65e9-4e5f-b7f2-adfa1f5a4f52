<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.common.mapper.CommonMapper">
    <select id="exists" resultType="java.lang.Boolean">
        <bind name="selectOneFlag" value="true"/>
        SELECT EXISTS (<include refid="selectByParam"/>)AS is_exist
    </select>
    <select id="longValue" resultType="java.lang.Long">
        <bind name="selectOneFlag" value="false"/>
        <include refid="selectByParam"/>
    </select>
    <select id="stringValue" resultType="java.lang.String">
        <bind name="selectOneFlag" value="false"/>
        <include refid="selectByParam"/>
    </select>
    <sql id="selectByParam">
        SELECT
        <choose>
            <when test="ew != null and ew.sqlSelect != null">
                ${ew.sqlSelect}
            </when>
            <when test="selectOneFlag">
                1
            </when>
            <otherwise>*</otherwise>
        </choose>
        <if test="ew.sqlSegment != null">
            FROM ${ew.tableName}
        </if>
        <if test="ew != null">
            <where>
                <if test="ew.sqlSegment != null and ew.sqlSegment != '' and ew.nonEmptyOfWhere">
                    <if test="ew.nonEmptyOfEntity and ew.nonEmptyOfNormal">AND</if>
                    ${ew.sqlSegment}
                </if>
            </where>

            <if test="ew.sqlSegment != null and ew.sqlSegment != '' and ew.emptyOfWhere">
                ${ew.sqlSegment}
            </if>
        </if>
        <choose>
            <when test="ew != null and ew.sqlComment != null">
                ${ew.sqlComment}
            </when>
            <otherwise/>
        </choose>
    </sql>

    <!-- 执行动态SQL查询 -->
    <select id="executeDynamicQuery" resultType="java.util.HashMap">
        ${sql}
    </select>

    <!-- 执行动态SQL查询（带参数） -->
    <select id="executeDynamicQueryWithParams" resultType="java.util.HashMap">
        ${sql}
    </select>

</mapper>

