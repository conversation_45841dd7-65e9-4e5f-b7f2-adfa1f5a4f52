package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 表单数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = FormConstant.TABLE_NAME_$, autoResultMap = true)
@JsonIgnoreProperties(ZdCommonConstant.IGNORE_PROPERTY)
public class ZdPageFormData extends ZdPageFormDataDTO {


    @JsonIgnore
    @ApiModelProperty("表单主键")
    private Long formId;

    @TableField(exist = false)
    @JsonIgnore
    @ApiModelProperty("表单数据")
    private Map<String, Object> formDataMap;


    public ZdPageFormData() {
    }

    public ZdPageFormData(Long id, String tableName) {
        this.setId(id);
        this.setTableName(tableName);

    }

    public ZdPageFormData(ZdPageFormDataDTO zdPageFormDataDTO) {
        BeanUtils.copyProperties(zdPageFormDataDTO, this);
    }

    public Map<String, Object> getFormDataMap() {
        String formData = getFormData();
        if (Objects.isNull(formDataMap) && Objects.nonNull(formData)) {
            formDataMap = JsonUtil.toMap(formData);
        }
        return formDataMap;
    }
}
