package com.sinitek.sirm.nocode.page.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthSaveDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthSearchParamDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageAuth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 2025-03-12 13:38:21
 * @description 针对表【zd_page_auth(页面权限表)】的数据库操作Mapper
 * @Entity com.sinitek.sirm.nocode.page.entity.ZdPageAuth
 */
public interface ZdPageAuthMapper extends BaseMapper<ZdPageAuth> {
    /**
     * 通过权限id 获取应用的code
     *
     * @param id 权限id
     * @return 应用编码
     */
    String getAppCodeByPageAuthId(@Param("id") Long id);

    Page<ZdPageAuthSaveDTO> getListByCond(Page<ZdPageAuthSaveDTO> page, @Param("dto") ZdPageAuthSearchParamDTO dto);

}




