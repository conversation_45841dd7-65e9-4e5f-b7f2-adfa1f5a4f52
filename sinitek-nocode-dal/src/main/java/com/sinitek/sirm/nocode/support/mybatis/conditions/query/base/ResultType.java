package com.sinitek.sirm.nocode.support.mybatis.conditions.query.base;

/**
 * <AUTHOR>
 * @version 2025.0418
 * @description 结果类型接口
 * @since 1.0.0-SNAPSHOT
 */
@FunctionalInterface
public interface ResultType {
    /**
     * 获取返回类型
     * <ul>
     *     <li>1.可以是实体，或者任意数据类型</li>
     *     <li>
     *         2.也支持resultMapId,需要在实体上开启autoResultMap 为true,例如： @TableName(value = "xx", autoResultMap = true)
     *         也支持在mapper 里面定义 的resultMap
     *     </li>
     * </ul>
     *
     * @return 返回类型
     */
    Object getReturnType();
}
