package com.sinitek.sirm.nocode.support.mybatis.conditions.query;

import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper;
import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.SharedString;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.Query;
import com.baomidou.mybatisplus.core.conditions.segments.MergeSegments;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.ArrayUtils;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.DialectFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.dialects.IDialect;
import com.sinitek.sirm.common.utils.SQLUtils;
import com.sinitek.sirm.nocode.common.support.mybatis.ITableName;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @version 2025.0509
 * @since 1.0.0-SNAPSHOT
 */

public class LamWrapper<T> extends AbstractLambdaWrapper<T, LamWrapper<T>>
        implements Query<LamWrapper<T>, T, SFunction<T, ?>>, ITableName {

    /**
     * 表名
     */
    private String tableName;
    /**
     * 查询字段
     */
    private SharedString sqlSelect = new SharedString();

    @Override
    protected LamWrapper<T> instance() {
        return new LamWrapper<>(getEntity(), getEntityClass(), null, paramNameSeq, paramNameValuePairs,
                new MergeSegments(), SharedString.emptyString(), SharedString.emptyString(), SharedString.emptyString());
    }

    /**
     * 不建议直接 new 该实例，使用 Wrappers.lambdaQuery(...)
     */
    LamWrapper(T entity, Class<T> entityClass, SharedString sqlSelect, AtomicInteger paramNameSeq,
               Map<String, Object> paramNameValuePairs, MergeSegments mergeSegments,
               SharedString lastSql, SharedString sqlComment, SharedString sqlFirst) {
        super.setEntity(entity);
        super.setEntityClass(entityClass);
        this.paramNameSeq = paramNameSeq;
        this.paramNameValuePairs = paramNameValuePairs;
        this.expression = mergeSegments;
        this.sqlSelect = sqlSelect;
        this.lastSql = lastSql;
        this.sqlComment = sqlComment;
        this.sqlFirst = sqlFirst;
    }

    @SafeVarargs
    @Override
    public final LamWrapper<T> select(SFunction<T, ?>... columns) {
        if (ArrayUtils.isNotEmpty(columns)) {
            this.sqlSelect.setStringValue(columnsToString(false, columns));
        }
        return typedThis;
    }

    public LamWrapper<T> select(String... columns) {
        if (ArrayUtils.isNotEmpty(columns)) {
            this.sqlSelect.setStringValue(String.join(",", columns));
        }
        return typedThis;
    }

    /**
     * max 最大
     *
     * @param column 列
     * @return lambdaQuery
     */
    public LamWrapper<T> max(SFunction<T, ?> column) {
        return select("max(" + columnsToString(false, column) + ")");
    }

    @Override
    public LamWrapper<T> select(Class<T> entityClass, Predicate<TableFieldInfo> predicate) {
        if (entityClass == null) {
            entityClass = getEntityClass();
        } else {
            setEntityClass(entityClass);
        }
        Assert.notNull(entityClass, "entityClass can not be null");
        this.sqlSelect.setStringValue(TableInfoHelper.getTableInfo(entityClass).chooseSelect(predicate));
        initTableName(entityClass);
        return typedThis;
    }

    /**
     * one 查询一条
     *
     * @return lambdaQuery
     */
    public LamWrapper<T> one() {
        DbType dbType = DbType.getDbType(SQLUtils.getDbType());
        IDialect dialect = DialectFactory.getDialect(dbType);
        String dialectSql = dialect.buildPaginationSql("", 0, 1).getDialectSql();
        if (StringUtils.isNotBlank(dialectSql)) {
            int count = StringUtils.countMatches(dialectSql, "?");
            if (count == 1) {
                dialectSql = dialectSql.replace("?", "1");
            } else if (count == 2) {
                dialectSql = dialectSql.replaceFirst("\\?", "0");
                dialectSql = dialectSql.replaceFirst("\\?", "1");
            }
            last(dialectSql);
        }
        return typedThis;
    }

    @Override
    public String getTableName() {
        // 需要调用下。
        getSqlSegment();
        return tableName;
    }

    @Override
    public String getSqlSelect() {
        return sqlSelect.getStringValue();
    }

    /**
     * 初始化表名
     *
     * @param entityClass 实体类
     */
    private void initTableName(Class<?> entityClass) {
        if (tableName == null) {
            TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
            Assert.notNull(tableInfo, "tableInfo can not be null");
            tableName = tableInfo.getTableName();
        }
    }

    @Override
    protected String columnToString(SFunction<T, ?> column, boolean onlyColumn) {
        if (Objects.isNull(tableName)) {
            initTableName(LambdaUtils.resolve(column).getInstantiatedType());
        }
        return super.columnToString(column, onlyColumn);
    }

    @Override
    public void clear() {
        super.clear();
        sqlSelect.toNull();
        tableName = null;
    }


    private LamWrapper(Class<T> entityClass) {
        super.setEntityClass(entityClass);
        super.initNeed();
        initTableName(entityClass);
    }

    private LamWrapper(T entity) {
        super.setEntity(entity);
        super.initNeed();
    }

    private LamWrapper() {
        this((T) null);
    }

    @Override
    public LamWrapper<T> setEntityClass(Class<T> entityClass) {
        initTableName(entityClass);
        super.setEntityClass(entityClass);
        return typedThis;
    }

    /**
     * 设置wrapper
     *
     * @param wrapper wrapper
     * @return this
     */
    public <R, Children extends AbstractWrapper<T, R, Children>> LamWrapper<T> withWrapper(Wrapper<T> wrapper) {
        if (wrapper instanceof LamWrapper) {
            return (LamWrapper<T>) wrapper;
        }
        if (wrapper instanceof AbstractWrapper) {
            AbstractWrapper<T, ?, Children> queryWrapper = (AbstractWrapper<T, ?, Children>) wrapper;
            setEntity(queryWrapper.getEntity());
            Class<T> entityClass = queryWrapper.getEntityClass();
            if (Objects.nonNull(entityClass)) {
                setEntityClass(entityClass);
            }
            this.paramNameSeq = (AtomicInteger) ReflectUtil.getFieldValue(queryWrapper, "paramNameSeq");
            this.paramNameValuePairs = queryWrapper.getParamNameValuePairs();
            this.expression = queryWrapper.getExpression();
            this.lastSql = (SharedString) ReflectUtil.getFieldValue(queryWrapper, "lastSql");
            this.sqlComment = new SharedString(queryWrapper.getSqlComment());
            this.sqlFirst = new SharedString(queryWrapper.getSqlFirst());
            boolean b = ReflectUtil.hasField(wrapper.getClass(), "sqlSelect");
            if (b) {
                this.sqlSelect = (SharedString) ReflectUtil.getFieldValue(queryWrapper, "sqlSelect");
            }
            return typedThis;
        }
        throw new MybatisPlusException("无法识别的Wrapper");
    }

    public static <T> LamWrapper<T> ins(Class<T> entityClass) {
        return new LamWrapper<>(entityClass);
    }

    public static <T> LamWrapper<T> ins(Wrapper<T> wrapper) {
        return new LamWrapper<T>().withWrapper(wrapper);
    }

    public static <T> LamWrapper<T> ins(String tableName) {
        LamWrapper<T> ins = new LamWrapper<>();
        ins.tableName = tableName;
        return ins;
    }

    /**
     * eq or in 操作
     *
     * @param column 列
     * @param o      值
     * @return lambdaQuery
     */
    public static <T, Children extends AbstractLambdaWrapper<T, Children>> Children eqOrIn(SFunction<T, ?> column, Object o, Children children) {
        if (!NULL_TEST.test(o)) {
            boolean isCollection = o instanceof Collection;
            boolean isArray = o.getClass().isArray();
            boolean eq = true;
            if (isCollection || isArray) {
                if (isCollection) {
                    Collection<?> collection = (Collection<?>) o;
                    if (collection.size() > 1) {
                        eq = false;
                        children.in(column, collection);
                    } else {
                        o = collection.iterator().next();
                    }
                } else {
                    Object[] array = (Object[]) o;
                    if (array.length > 1) {
                        eq = false;
                        children.in(column, array);
                    } else {
                        o = array[0];
                    }

                }
            }
            if (eq) {
                children.eq(column, o);
            }
        }
        return children;
    }

    public static <T> LamWrapper<T> eqOrIn(SFunction<T, ?> column, Object o) {
        return eqOrIn(column, o, new LamWrapper<>());
    }


    /**
     * 静态的判空数据
     */
    private static final Predicate<Object> NULL_TEST = (a) -> {
        boolean b = Objects.isNull(a);
        if (b) {
            return true;
        }
        if (a instanceof Collection) {
            return CollectionUtils.isEmpty((Collection<?>) a);
        }
        if (a.getClass().isArray()) {
            Object[] objects = (Object[]) a;
            return objects.length == 0;
        }
        return false;
    };
}
