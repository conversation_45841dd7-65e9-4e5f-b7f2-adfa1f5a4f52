package com.sinitek.sirm.nocode.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * 应用管理员
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "应用管理员实体")
@TableName(value = "zd_app_manager")
public class ZdAppManager implements Serializable {
    @TableId("ID")
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;

    /**
     * 应用编码
     */
    @ApiModelProperty(value = "应用编码",example = "abc1000")
    private String appCode;

    /**
     * orgId
     */
    @ApiModelProperty("组织id")
    private String orgId;

    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}
