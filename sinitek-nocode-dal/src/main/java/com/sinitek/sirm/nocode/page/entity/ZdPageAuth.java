package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.common.support.handler.ListStringTypeHandler;
import com.sinitek.sirm.nocode.page.enumerate.DataScopeEnum;
import com.sinitek.sirm.nocode.page.enumerate.FieldAuthFlagEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.support.validator.ZdPageAuthValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.util.List;

/**
 * 页面权限表
 *
 * @TableName zd_page_auth
 */
@Data
@ApiModel(description = "页面权限实体")
@TableName(value = "zd_page_auth", autoResultMap = true)
@ValidatorAll(value = ZdPageAuthValidator.class)
public class ZdPageAuth implements Serializable {

    @TableId("ID")
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;

    /**
     * 页面编码
     */
    @NotBlank(message = "页面编码不能为空")
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String pageCode;

    /**
     * 权限类型，0：查看，1：新增，2：编辑，3：删除，4：导出，5：导入
     */
    @NotNull(message = "权限类型不能为空")
    @ApiEnumProperty(required = true, example = "0")
    private PageAuthTypeEnum authType;

    /**
     * 人员id，多个用逗号隔开
     */
    @TableField(typeHandler = ListStringTypeHandler.class)
    @ApiModelProperty(value = "人员id，多个用逗号隔开", example = "[\"121212\",\"11111\"]")
    private List<String> orgIds;

    /**
     * 数据权限范围，0：全部，1：本人，2：本部门，3：本部门及子部门，4：自定义
     */
    @NotNull(message = "数据权限范围不能为空")
    @ApiEnumProperty(required = true, example = "0")
    private DataScopeEnum dataScope;

    /**
     * 字段权限，当为空时，为表单字段字段状态，有具体的权限控制的话，是个json数据
     */
    @ApiModelProperty(value = "自定义字段权限，是个json字符数据", example = "\"{name:\"read\"}\"")
    @TableField(value = "field_auth_data", typeHandler = JsonbTypeHandler.class)
    private String fieldAuthData;

    @NotNull(message = "字段权限枚举不能为空")
    @ApiEnumProperty(example = "0", required = true)
    private FieldAuthFlagEnum fieldAuth;

    /**
     * 自定义数据权限范围
     */
    @ApiModelProperty(value = "自定义数据权限范围")
    @TableField(value = "custom_data_scope", typeHandler = FormDataScopeCustomConditionTypeHandler.class)
    private ZdFormDataScopeCustomConditionDTO customDataScope;

    @TableField(exist = false)
    @ApiModelProperty(value = "组织结构信息")
    private List<ZdOrgObjectDTO> memberList;

    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}
