<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.page.mapper.ZdPageAuthMapper">

    <select id="getAppCodeByPageAuthId" resultType="java.lang.String">
        select p.app_code
        from zd_page_auth a
                 inner join
             zd_page p on p.code = a.page_code
        where a.id = #{id}
    </select>


    <select id="getListByCond" resultType="com.sinitek.sirm.nocode.page.dto.ZdPageAuthSaveDTO"
            parameterType="com.sinitek.sirm.nocode.page.dto.ZdPageAuthSearchParamDTO">
        select *
        from zd_page_auth a
        where 1=1
        <if test="dto.pageCode != null and dto.pageCode != ''">
            AND a.page_code = #{dto.pageCode}
        </if>
        <if test="dto.authType != null">
            AND a.auth_type = #{dto.authType}
        </if>
        <if test="dto.name != null and dto.name != ''">
            AND a.name like concat('%',#{dto.name},'%')
        </if>
        ORDER BY a.sort,a.id desc
    </select>
</mapper>
