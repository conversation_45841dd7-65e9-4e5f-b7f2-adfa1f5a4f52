<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.page.mapper.ZdPageAuthMapper">

    <select id="getAppCodeByPageAuthId" resultType="java.lang.String">
        select p.app_code
        from zd_page_auth a
                 inner join
             zd_page p on p.code = a.page_code
        where a.id = #{id}
    </select>
</mapper>
