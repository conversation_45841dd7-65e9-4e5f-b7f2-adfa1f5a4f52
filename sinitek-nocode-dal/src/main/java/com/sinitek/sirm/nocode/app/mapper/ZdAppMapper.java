package com.sinitek.sirm.nocode.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.nocode.app.dto.ZdAppDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSearchParamDTO;
import com.sinitek.sirm.nocode.app.entity.ZdApp;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 2025-03-11 09:32:06
 * @description 针对表【zd_app(应用表)】的数据库操作Mapper
 * @Entity generator.domain.ZdApp
 */
public interface ZdAppMapper extends BaseMapper<ZdApp> {
    /**
     * 分页查询应用
     *
     * @param page  页码数据
     * @param param 参数
     * @return 分页数据
     */

    IPage<ZdAppDTO> search(IPage<ZdAppDTO> page, @Param("params") ZdAppSearchParamDTO param);

    /**
     * 更改应用的状态
     *
     * @param appCode 应用编码
     * @return 影响行数
     */
    Integer updateStatus(@Param("appCode") String appCode);


}




