package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.common.support.json.Base64Encode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * 页面表单历史表
 *
 * @TableName zd_page_form_history
 */
@Data
@ApiModel(description = "历史表单实体")
@TableName(value = "zd_page_form_history")
public class ZdPageFormHistory implements Serializable {

    @TableId("ID")
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;

    /**
     * 表单配置
     */
    @JsonSerialize(using = Base64Encode.class)
    @TableField(typeHandler = JsonbTypeHandler.class)
    @ApiModelProperty(value = "表单配置")
    private String pageData;

    /**
     * 页面表单id
     */
    @ApiModelProperty(value = "页面表单id")
    private Long pageFormId;

    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}
