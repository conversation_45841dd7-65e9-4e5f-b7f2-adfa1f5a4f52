package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * 表单数据显示设置
 *
 * @TableName zd_page_form_show_config
 */
@Data
@ApiModel(description = "表单数据显示实体")
@TableName(value = "zd_page_form_show_config")
public class ZdPageFormShowConfig implements Serializable {

    @TableId("ID")
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;

    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码")
    private String formCode;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String orgId;

    /**
     * 是个json对象，例如:[{key:name,showOrder:0}]
     */
    @ApiModelProperty(value = "显示配置，是个json对象")
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String showConfig;

    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}
