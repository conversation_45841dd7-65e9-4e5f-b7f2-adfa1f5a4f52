package com.sinitek.sirm.nocode.page.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.data.model.tree.cache.NodeIdConcurrencyCache;
import com.sinitek.data.model.tree.enumerate.TypeEnum;
import com.sinitek.data.model.tree.service.impl.BaseTreeServiceImpl;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.nocode.page.entity.ZdPage;
import com.sinitek.sirm.nocode.page.mapper.ZdPageMapper;
import com.sinitek.sirm.tenant.annotation.TenantFilterShutDown;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0407
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@Repository
public class ZdPageDAO extends BaseTreeServiceImpl<ZdPageMapper, ZdPage> {

    @Resource
    private NodeIdConcurrencyCache nodeIdConcurrencyCache;


    @Override
    @Transactional(
            rollbackFor = {Exception.class}
    )
    @TenantFilterShutDown
    public void addNode(ZdPage preNode, ZdPage newNode, TypeEnum type) {
        // 经过和框架组沟通，目前无法支持森林模型，故需要修改该方法
        super.addNode(preNode, newNode, type);
        resetCodeVal(newNode);
    }


    @Override
    @Transactional(
            rollbackFor = {Exception.class}
    )
    @TenantFilterShutDown
    public void resetNode(ZdPage node) {
        super.resetNode(node);
        resetCodeVal(node);
    }

    public void resetCodeVal(ZdPage node) {
        Long parentId = node.getParentId();
        if (Objects.equals(parentId, 0L)) {
            LambdaQueryWrapper<ZdPage> query = Wrappers.<ZdPage>lambdaQuery()
                    .select(ZdPage::getCodeVal, ZdPage::getId)
                    .eq(ZdPage::getParentId, 0);
            List<ZdPage> list = list(query);
            Integer count = list.stream().filter(a -> !Objects.equals(a.getId(), node.getId())).map(a -> {
                        String codeVal = a.getCodeVal();
                        return NumberTool.safeToInteger(codeVal.substring(0, codeVal.lastIndexOf(";")), 1);
                    }).max((Comparator.comparingInt(o -> o)))
                    .orElse(0) + 1;
            LambdaUpdateWrapper<ZdPage> updateQuery = Wrappers.<ZdPage>lambdaUpdate()
                    .set(ZdPage::getCodeVal, count + ";")
                    .eq(ZdPage::getId, node.getId());

            update(updateQuery);
        }
    }


    @Override
    public List<ZdPage> findDirectChildren(ZdPage node) {
        if (Objects.isNull(node)) {
            return list(Wrappers.<ZdPage>lambdaQuery().eq(ZdPage::getParentId, 0L));
        }
        return super.findDirectChildren(node);
    }
}
