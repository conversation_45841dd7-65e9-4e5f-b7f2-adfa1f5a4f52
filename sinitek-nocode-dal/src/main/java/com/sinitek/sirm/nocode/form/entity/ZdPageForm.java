package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面表单表
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_form", autoResultMap = true)
@Data
@JsonIgnoreProperties(ZdCommonConstant.IGNORE_PROPERTY)
public class ZdPageForm extends ZdPageFormDTO {

}
