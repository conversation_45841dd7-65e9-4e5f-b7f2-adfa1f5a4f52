package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sinitek.data.model.version.entity.BaseVersionEntity;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.common.mybaits.CustomBaseAudit;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.common.support.json.Base64Decode;
import com.sinitek.sirm.nocode.common.support.json.Base64Encode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 页面表单表
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_form", autoResultMap = true)
@Data
@ApiModel(description = "表单实体")
@JsonIgnoreProperties(ZdCommonConstant.IGNORE_PROPERTY)
public class ZdPageForm extends BaseVersionEntity implements CustomBaseAudit {

    /**
     * 表单名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "表单名称", example = "请假")
    private String name;

    /**
     * 更新人名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "更新人名称", example = "管理员")
    private String updater;

    /**
     * 页面编码
     */
    @ApiModelProperty(value = "页面编码", example = "a10000")
    private String pageCode;

    /**
     * 表单编码
     */
    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty(value = "表单编码", example = "as256")
    private String code;

    /**
     * 表单配置
     */
    @JsonDeserialize(using = Base64Decode.class)
    @JsonSerialize(using = Base64Encode.class)
    @ApiModelProperty(value = "表单配置")
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String pageData;

    @JsonIgnore
    @ApiModelProperty(value = "更新人ID")
    @TableField(
            value = "updater_id",
            fill = FieldFill.INSERT_UPDATE
    )
    private String updaterId;

    @Override
    public void setCreatorId(String creatorId) {
        // Implementation can be added if needed
    }
}
