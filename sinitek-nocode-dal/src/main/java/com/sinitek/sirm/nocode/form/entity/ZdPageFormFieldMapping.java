package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * 表单字段映射表实体类
 *
 * <AUTHOR>
 * @version 2025.0604
 * @TableName zd_page_form_field_mapping
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "表单配置表实体")
@TableName(value = "zd_page_form_field_mapping")
public class ZdPageFormFieldMapping implements Serializable, FormCodeSupplier {

    @TableId("ID")
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;

    /**
     * 表单编码
     */
    @NotBlank(message = "表单编码不能为空")
    @Length(max = 50, message = "表单编码不能超过50个字符")
    @ApiModelProperty(value = "表单编码", example = "form_123456", required = true)
    private String formCode;

    /**
     * 自定义字段名称
     */
    @NotBlank(message = "自定义字段名称不能为空")
    @Length(max = 300, message = "自定义字段名称不能超过300个字符")
    @ApiModelProperty(value = "自定义字段名称", example = "用户姓名", required = true)
    private String name;

    /**
     * 自定义字段的code
     */
    @Length(max = 100, message = "自定义字段的code不能超过100个字符")
    @ApiModelProperty(value = "自定义字段的code", example = "user_name")
    private String code;

    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}
