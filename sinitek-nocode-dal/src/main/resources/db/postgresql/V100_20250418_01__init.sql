create table if not exists zd_app
(
    id              bigint       not null
        primary key,
    name            varchar(100) not null,
    code            varchar(50)  not null,
    app_secret      varchar(500) not null,
    description     varchar(1000),
    iconfont        varchar(500),
    background      varchar(500),
    status          smallint     not null,
    creator_id      varchar(30)  not null,
    updater_id      varchar(30)  not null,
    version         integer      not null,
    createtimestamp timestamp(0) not null,
    updatetimestamp timestamp(0) not null
);

comment on table zd_app is '应用表';

comment on column zd_app.id is '主键';

comment on column zd_app.name is '应用名称';

comment on column zd_app.code is '应用编码';

comment on column zd_app.app_secret is '密钥';

comment on column zd_app.description is '应用描述';

comment on column zd_app.iconfont is '应用图标类型';
comment on column zd_app.background is '应用背景';

comment on column zd_app.status is '状态，0:未启用，1:已经启用';

comment on column zd_app.creator_id is '创建人id';

comment on column zd_app.updater_id is '更改人id';

comment on column zd_app.version is '乐观锁';

comment on column zd_app.createtimestamp is '创建时间';

comment on column zd_app.updatetimestamp is '修改时间';

create index in_zd_app_code
    on zd_app (code);

create table if not exists zd_app_manager
(
    id       bigint      not null
        primary key,
    app_code varchar(50) not null,
    org_id   varchar(30) not null
);

comment on table zd_app_manager is '应用管理员';

comment on column zd_app_manager.id is '主键';

comment on column zd_app_manager.app_code is '应用编码';

comment on column zd_app_manager.org_id is '用户id';

create index in_zd_app_manager_app_code_org_id
    on zd_app_manager (app_code, org_id);


create table if not exists zd_app_oa_integration
(
    id            bigint      not null
        primary key,
    app_code      varchar(50) not null,
    platform_type smallint    not null,
    param         jsonb       not null
);

comment on table zd_app_oa_integration is '应用OA集成表';

comment on column zd_app_oa_integration.id is '主键';

comment on column zd_app_oa_integration.app_code is '应用编码';

comment on column zd_app_oa_integration.platform_type is '平台类型';

comment on column zd_app_oa_integration.param is '参数，不同平台的参数类型不一样';


create index if not exists in_zd_app_oa_integration_app_code
    on zd_app_oa_integration (app_code);

create table if not exists zd_page
(
    id              bigint       not null
        primary key,
    app_code        varchar(50)  not null,
    name            varchar(100) not null,
    code            varchar(50)  not null,
    url             varchar(50)  null,
    page_type       smallint     not null,
    sort            integer      not null,
    parent_id       bigint,
    high_val        integer,
    code_val        varchar(50),
    thread_id       bigint,
    version         integer      not null,
    createtimestamp timestamp(0) not null,
    updatetimestamp timestamp(0) not null
);

comment on table zd_page is '页面表';

comment on column zd_page.id is '主键';

comment on column zd_page.app_code is '应用编码';
comment on column zd_page.code is '页面编码';
comment on column zd_page.url is '页面地址';

comment on column zd_page.name is '名称';

comment on column zd_page.page_type is '页面类型，有三种，0:普通表单，1：流程表单，2:报表表单,9：分组';

comment on column zd_page.sort is '排序';

comment on column zd_page.parent_id is '父级节点';

comment on column zd_page.high_val is '树状模型的节点高度';

comment on column zd_page.code_val is '树状模型的节点编码';

comment on column zd_page.thread_id is '跟踪id';

comment on column zd_page.version is '乐观锁';

comment on column zd_page.createtimestamp is '创建时间';
comment on column zd_page.updatetimestamp is '修改时间';

create index in_zd_page_code
    on zd_page (code);

create table if not exists zd_page_base_setting
(
    id               bigint      not null
        primary key,
    page_code        varchar(50) not null,
    jump_param       varchar(1000),
    submit_jump_type smallint    not null,
    submit_jump_url  varchar(500)
);

comment on table zd_page_base_setting is '页面的基本设置表';

comment on column zd_page_base_setting.id is '主键';

comment on column zd_page_base_setting.page_code is '页面编码';

comment on column zd_page_base_setting.jump_param is '页面提交后跳转参数';

comment on column zd_page_base_setting.submit_jump_type is '页面提交后跳转的类型，有三种，0:跳转默认页面，1：应用内页面，2:外部链接';

comment on column zd_page_base_setting.submit_jump_url is '页面提交后跳转的地址';

create index in_zd_page_base_setting_page_code
    on zd_page_base_setting (page_code);

create table if not exists zd_page_auth
(
    id                bigint       not null
        primary key,
    page_code         varchar(50)  not null,
    sort              integer      not null,
    auth_type         smallint     not null,
    name              varchar(100) not null,
    description       varchar(1000),
    member_type       smallint     not null,
    member_org_ids    jsonb,
    operation_auth    varchar(50)  not null,
    data_scope        varchar(50),
    field_auth        jsonb,
    department_ids    jsonb,
    custom_data_scope jsonb
);

comment on table zd_page_auth is '页面权限表';

comment on column zd_page_auth.id is '主键';

comment on column zd_page_auth.page_code is '页面编码';

comment on column zd_page_auth.sort is '权限排序';

comment on column zd_page_auth.auth_type is '数据权限类型，0:提交，1:数据权限';

comment on column zd_page_auth.name is '权限名称';

comment on column zd_page_auth.description is '权限描述';

comment on column zd_page_auth.member_type is '成员类型，0：所有成员,1:自定义';

comment on column zd_page_auth.member_org_ids is '当成员类型选择为自定的时候，存储人员id,是个人员id数组';
comment on column zd_page_auth.department_ids is '当数据类型为自定义部门时，存储自定义部门数据,是部门orgId数组';

comment on column zd_page_auth.operation_auth is '操作权限，多个用逗号隔开';

comment on column zd_page_auth.data_scope is '数据范围，多个用逗号隔开';

comment on column zd_page_auth.field_auth is '字段权限，当为空时，为表单字段字段状态，有具体的权限控制的话，是个json数据';

comment on column zd_page_auth.custom_data_scope is '自定义数据权限范围';


create index in_zd_page_auth_page_code
    on zd_page_auth (page_code);

create table zd_page_scene
(
    id                  bigint      not null
        primary key,
    page_code           varchar(50) not null,
    scene_type          smallint    not null,
    submit_type         smallint    not null,
    repeat_type         smallint,
    start_time          time,
    holiday_strategy    smallint,
    end_date            date,
    report_send_org_ids jsonb,
    note_type           varchar(30),
    finish_date         date,
    next_execute_date   date
);

comment on table zd_page_scene is '页面场景设置';

comment on column zd_page_scene.id is '主键';

comment on column zd_page_scene.page_code is '页面编码';

comment on column zd_page_scene.scene_type is '场景类型，0：通用场景，1：收集表';

comment on column zd_page_scene.submit_type is '提交类型，0：无限制，1：每个人限制一次';

comment on column zd_page_scene.repeat_type is '重复类型，0：每天，1：每周，2：每月';

comment on column zd_page_scene.start_time is '开始时间';

comment on column zd_page_scene.holiday_strategy is '是否跳过节假日';

comment on column zd_page_scene.end_date is '截至时间';

comment on column zd_page_scene.report_send_org_ids is '报告发送人,数组';

comment on column zd_page_scene.note_type is '报告通知类型，多个用英文逗号隔开，详细请查看枚举 NoteTypeEnum';

comment on column zd_page_scene.finish_date is '任务完成日期';
comment on column zd_page_scene.next_execute_date is '下一个任务执行日期';

create index in_zd_page_scene_page_code
    on zd_page_scene (page_code);

create table if not exists zd_page_form
(
    id                 bigint       not null
        primary key,
    page_code          varchar(50)  not null,
    code               varchar(50)  not null,
    page_data          jsonb,
    publish_status     smallint,
    publish_version    integer,
    publish_date       timestamp(0),
    thread_id          bigint,
    thread_latest_flag smallint,
    latest_flag        smallint,
    version            integer      not null,
    createtimestamp    timestamp(0) not null,
    updatetimestamp    timestamp(0) not null
);

comment on table zd_page_form is '页面表单表';

comment on column zd_page_form.id is '主键';

comment on column zd_page_form.page_code is '页面编码';

comment on column zd_page_form.code is '表单编码';

comment on column zd_page_form.page_data is '表单配置';

comment on column zd_page_form.publish_status is '发布状态';

comment on column zd_page_form.publish_version is '发布版本';

comment on column zd_page_form.publish_date is '发布日期';

comment on column zd_page_form.thread_id is '线索id';

comment on column zd_page_form.thread_latest_flag is '标记最新生成的记录';

comment on column zd_page_form.latest_flag is '标记最新有效的记录';

comment on column zd_page_form.version is '乐观锁';

comment on column zd_page_form.createtimestamp is '创建时间';

comment on column zd_page_form.updatetimestamp is '修改时间';

create index in_zd_page_form_code
    on zd_page_form (code);
create index in_zd_page_form_page_code
    on zd_page_form (page_code);


create table if not exists zd_page_form_config
(
    id          bigint      not null
        primary key,
    form_code   varchar(50) not null,
    table_name  varchar(30) not null,
    processcode varchar(50)
);

comment on table zd_page_form_config is '表单配置表';

comment on column zd_page_form_config.id is '主键';

comment on column zd_page_form_config.form_code is '表单编码';

comment on column zd_page_form_config.table_name is '数据表的名称,当为普通表单或者是流程表单时，这个字段有值';

comment on column zd_page_form_config.processcode is '当表单为流程表单时，这个字段有值';


create index in_zd_page_form_config_form_code
    on zd_page_form_config (form_code);

create table if not exists zd_page_form_history
(
    id           bigint not null
        primary key,
    page_data    jsonb  not null,
    page_form_id bigint not null
        references zd_page_form (id)
);

comment on table zd_page_form_history is '页面表单历史表';

comment on column zd_page_form_history.id is '主键';

comment on column zd_page_form_history.page_data is '表单配置';

comment on column zd_page_form_history.page_form_id is '页面表单id';

create table if not exists zd_page_form_show_config
(
    id          bigint      not null
        primary key,
    form_code   varchar(50) not null,
    org_id      varchar(30) not null,
    show_config jsonb       not null
);

comment on table zd_page_form_show_config is '表单数据显示设置';

comment on column zd_page_form_show_config.id is '主键';

comment on column zd_page_form_show_config.form_code is '表单编码';

comment on column zd_page_form_show_config.org_id is '用户id';

comment on column zd_page_form_show_config.show_config is '是个json对象，例如:[{key:name,showOrder:0}]';


create index in_zd_page_form_show_config_form_code
    on zd_page_form_show_config (form_code);


drop function if exists create_page_form_data_batch;
create function create_page_form_data_batch(total_count integer) returns integer as
$$
DECLARE
    table_prefix          TEXT := 'zd_page_form_data'; -- 表名前缀
    start_index           int;-- 开始点
    table_counter         INT ;
    table_end             INT;
    dynamic_table_name    TEXT;
    dynamic_columns       TEXT;
    dynamic_comment       TEXT;
    table_start_index_sql TEXT;
BEGIN
    table_start_index_sql := 'select COALESCE(max(SUBSTRING(tablename FROM 19)::INTEGER),-1) as index
from pg_tables
where schemaname = current_schema()
  and tablename like ''zd_page_form_data%''';
    execute table_start_index_sql into start_index;
    table_counter := start_index + 1;
    table_end := table_counter + total_count;
    RAISE NOTICE '局部变量 start_index 的值为: %', start_index;
    RAISE NOTICE '局部变量 table_counter 的值为: %', table_counter;
    RAISE NOTICE '局部变量 table_end 的值为: %', table_end;
    -- 循环处理日期范围
    WHILE table_counter < table_end
        LOOP
            -- 生成动态表名 (例如: logs_2023_01_01)
            dynamic_table_name := format(
                    '%s_%s',
                    table_prefix,
                    table_counter::TEXT);

            -- 动态生成带日期的列 (可选逻辑)
            dynamic_columns := 'id bigint      not null
        primary key,
    form_data jsonb not null ,
    submit_field jsonb,
     approve_status int , status int ,
     form_id bigint ,
     report_date date ,
    creator_id varchar(30) not null ,
    updater_id varchar(30) not null ,
    version         int           not null,
    createtimestamp timestamp(0)  not null,
    updatetimestamp timestamp(0)  not null';

            dynamic_comment := chr(10) || format('comment on table %s is ''表单数据'';', dynamic_table_name);
            dynamic_comment :=
                    chr(10) || dynamic_comment || format('comment on column %s.id is ''主键'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.form_data is ''表单数据'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.submit_field is ''提交字段'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.approve_status is ''审批状态，应用于审批模型'';',
                                      dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.status is ''业务数据状态'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.form_id is ''表单主键'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.report_date is ''报告日期'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.version is ''业务数据状态'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.creator_id is ''创建人id'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.updater_id is ''更改人id'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.createtimestamp is ''创建时间'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('comment on column %s.updatetimestamp is ''修改时间'';', dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('create index %s_form_data_gin on %s using gin(form_data);',
                                      dynamic_table_name,
                                      dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('create index %s_creator_id_index on %s (creator_id);', dynamic_table_name,
                                      dynamic_table_name);
            dynamic_comment := chr(10) || dynamic_comment ||
                               format('create index %s_form_id_index on %s (form_id);', dynamic_table_name,
                                      dynamic_table_name);

            -- 执行建表语句
            EXECUTE format(
                    'CREATE TABLE IF NOT EXISTS %I (%s); %s',
                    dynamic_table_name,
                    dynamic_columns,
                    dynamic_comment
                    );

            table_counter := table_counter + 1;
        END LOOP;

    RETURN table_counter; -- 返回创建的表数量
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Error creating tables: %', SQLERRM;
END;
$$ language plpgsql;

--CREATE EXTENSION IF NOT EXISTS pg_trgm;
select create_page_form_data_batch(10);