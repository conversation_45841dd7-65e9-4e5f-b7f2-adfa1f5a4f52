create table if not exists zd_page_form_field_mapping
(
    id        bigint       not null
        primary key,
    form_code varchar(50)  not null,
    name      varchar(300) not null,
    code      varchar(100)
);

comment on table zd_page_form_field_mapping is '表单字段映射表';

comment on column zd_page_form_field_mapping.id is '主键';

comment on column zd_page_form_field_mapping.form_code is '表单编码';

comment on column zd_page_form_field_mapping.name is '自定义字段名称';
comment on column zd_page_form_field_mapping.code is '自定义字段的code';


create index in_zd_page_form_field_mapping_form_code_name
    on zd_page_form_field_mapping (form_code, name);