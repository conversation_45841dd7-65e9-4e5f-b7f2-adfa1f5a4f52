create table if not exists zd_page_form_ai_question_setting
(
    id                       bigint        not null
        primary key,
    form_code                varchar(50)   not null,
    model_code               varchar(50)   not null,
    input_content            varchar(3000) not null,
    input_var                jsonb,
    out_content              varchar(3000),
    out_position             smallint default 0,
    out_format               smallint default 0,
    markdown_conversion      smallint,
    markdown_conversion_type varchar(10),
    out_way                  smallint default 0

);

comment on table zd_page_form_ai_question_setting is '智搭ai问答设置';

comment on column zd_page_form_ai_question_setting.id is '主键';
comment on column zd_page_form_ai_question_setting.form_code is '表单编码';
comment on column zd_page_form_ai_question_setting.model_code is '模型编码';
comment on column zd_page_form_ai_question_setting.input_content is '输入内容';
comment on column zd_page_form_ai_question_setting.input_var is '输入参数';
comment on column zd_page_form_ai_question_setting.out_content is '输出内容';
comment on column zd_page_form_ai_question_setting.out_position is '输出位置';
comment on column zd_page_form_ai_question_setting.out_format is '输出格式';
comment on column zd_page_form_ai_question_setting.markdown_conversion is '是否进行智能markdown转换';
comment on column zd_page_form_ai_question_setting.markdown_conversion_type is '当进行markdown转换时，允许的格式';
comment on column zd_page_form_ai_question_setting.out_way is '输出方式';

create index if not exists in_zd_page_form_ai_question_setting_form_code
    on zd_page_form_ai_question_setting (form_code);
